package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.request.KeyValidationRequest;
import my.com.mandrill.component.service.KeyRequestIntegrationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("key-requests")
@RequiredArgsConstructor
public class KeyRequestController {

	private final KeyRequestIntegrationService keyRequestIntegrationService;

	@Hidden
	@PostMapping("private/validate")
	public void validateKey(@Valid @RequestBody KeyValidationRequest request) {
		log.info("validation integration: {}", request.getRequestKeyType());
		keyRequestIntegrationService.withValidateKey(request.getKey(), request.getRequestKeyType(), () -> {
			log.info("validation integration success");
			return null;
		});
	}

}
