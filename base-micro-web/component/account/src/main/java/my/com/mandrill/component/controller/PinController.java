package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.request.ChangePinRequest;
import my.com.mandrill.component.dto.request.CreatePinRequest;
import my.com.mandrill.component.dto.request.PinValidateRequest;
import my.com.mandrill.component.dto.request.UpdatePinResetRequest;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import my.com.mandrill.utilities.general.util.PhoneNumberUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("pin")
@RequiredArgsConstructor
public class PinController {

	private final AccountService accountService;

	private final UserService userService;

	private final Map<String, PasscodeProviderService> passcodeProviderService;

	private final ValidationService validationService;

	private final PinIntegrationService pinIntegrationService;

	private final KeyRequestService keyRequestService;

	private final AppUserService appUserService;

	@PostMapping
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public void createPin(@Valid @RequestBody CreatePinRequest request) {
		User user = userService.getCurrentUser();
		accountService.createPin(user, request);
	}

	@PutMapping
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public void changePin(@Valid @RequestBody ChangePinRequest request) {
		User user = accountService.getCurrentUser();
		pinIntegrationService.changePin(user, request);
	}

	@PutMapping("/reset")
	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public void updateResetPin(@Valid @RequestBody UpdatePinResetRequest request) {
		request.setUsername(switch (request.getUsernameType()) {
			case PHONE_NUMBER ->
				PhoneNumberUtil.getPhoneNumberWithNoCountryCode(request.getUsername()).getCombinedPhoneNumber();
			case EMAIL -> request.getUsername();
		});
		passcodeProviderService.get(PasscodeType.PIN.getProvider()).reset(request, request.getUsernameType());
	}

	@PostMapping("/validate")
	public ResponseEntity<KeyResponse> validatePin(@Valid @RequestBody PinValidateRequest request) {
		log.info("validating key request validating pin");
		validationService.validatePasscodeVerificationRequestKey(request.requestKeyType());
		User user = userService.getCurrentUser();
		AppUser appUser = appUserService.findByRefNo(SecurityUtil.currentUserLogin());
		log.info("validate and generate key user request");
		UserKeyRequest keyRequest = pinIntegrationService.validate(request.pin(), appUser, () -> {
			log.info("post process execute...");
			return keyRequestService.createRequestKey(user, request.requestKeyType());
		});

		log.info("user key request succeed");
		KeyResponse keyResponse = new KeyResponse();
		keyResponse.setKey(keyRequest.getKeyValue());
		return ResponseEntity.ok(keyResponse);
	}

}
