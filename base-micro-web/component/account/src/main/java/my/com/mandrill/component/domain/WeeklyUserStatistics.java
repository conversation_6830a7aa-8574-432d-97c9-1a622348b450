package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import my.com.mandrill.component.constant.UserStatisticType;
import my.com.mandrill.utilities.core.audit.AuditSection;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.time.Year;
import java.util.Objects;

@Getter
@Setter
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "weekly_user_statistics_transaction",
		uniqueConstraints = { @UniqueConstraint(columnNames = { "week", "year", "type" }) })
public class WeeklyUserStatistics extends AuditSection implements Serializable {

	@NotNull
	@Column(name = "week", nullable = false)
	private int week;

	@NotNull
	@Column(name = "year", nullable = false)
	private Year year;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "type", nullable = false)
	private UserStatisticType type;

	@NotNull
	@Column(name = "is_synchronized", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private boolean isSynchronized = false;

	@NotNull
	@Column(name = "value", nullable = false)
	private long value;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		WeeklyUserStatistics weeklyUserStatistics = (WeeklyUserStatistics) o;
		return getId() != null && Objects.equals(getId(), weeklyUserStatistics.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}