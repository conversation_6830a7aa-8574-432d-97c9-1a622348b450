package my.com.mandrill.component.dto.model;

import lombok.AllArgsConstructor;
import my.com.mandrill.component.constant.AuthenticationProviderSource;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.user.OAuth2User;

import java.util.Collection;
import java.util.Map;

@AllArgsConstructor
public class CustomOAuth2User implements OAuth2User {

	private OAuth2UserRequest oAuth2UserRequest;

	private OAuth2User oAuth2User;

	@Override
	public Map<String, Object> getAttributes() {
		return oAuth2User.getAttributes();
	}

	@Override
	public Collection<? extends GrantedAuthority> getAuthorities() {
		return oAuth2User.getAuthorities();
	}

	@Override
	public String getName() {
		return oAuth2User.getAttribute("name");
	}

	public String getEmail() {
		// for FACEBOOK, email attribute cannot be get unless the app is being reviewed,
		// see you in prod.
		return oAuth2User.getAttribute("email");
	}

	// TODO FACEBOOK first_name and last_name is not returned.
	public String getFirstName() {
		switch (AuthenticationProviderSource
				.valueOf(oAuth2UserRequest.getClientRegistration().getRegistrationId().toUpperCase())) {
			case FACEBOOK:
				return oAuth2User.getAttribute("name");
			case GOOGLE:
				return oAuth2User.getAttribute("given_name");
			default:
				return null;
		}
	}

	public String getLastName() {
		switch (AuthenticationProviderSource
				.valueOf(oAuth2UserRequest.getClientRegistration().getRegistrationId().toUpperCase())) {
			case FACEBOOK:
				return null;
			case GOOGLE:
				return oAuth2User.getAttribute("family_name");
			default:
				return null;
		}
	}

}
