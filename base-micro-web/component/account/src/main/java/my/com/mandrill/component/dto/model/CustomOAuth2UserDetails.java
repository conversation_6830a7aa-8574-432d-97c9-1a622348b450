package my.com.mandrill.component.dto.model;

import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.component.domain.User;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.OAuth2User;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public class CustomOAuth2UserDetails implements OAuth2User, UserDetails {

	private String id;

	private String username;

	private String password;

	private Collection<? extends GrantedAuthority> authorities;

	@Setter
	private Map<String, Object> attributes;

	public CustomOAuth2UserDetails(String id, String username, String password,
			Collection<? extends GrantedAuthority> authorities) {
		this.id = id;
		this.username = username;
		this.password = password;
		this.authorities = authorities;
	}

	public static CustomOAuth2UserDetails create(User user) {
		return new CustomOAuth2UserDetails(user.getId(), user.getUsername(), user.getPassword(), user.getAuthorities()
				.stream().map(a -> new SimpleGrantedAuthority(a.getName())).collect(Collectors.toList()));
	}

	public static CustomOAuth2UserDetails create(User user, Map<String, Object> attributes) {
		CustomOAuth2UserDetails customOAuth2UserDetails = CustomOAuth2UserDetails.create(user);
		customOAuth2UserDetails.setAttributes(attributes);
		return customOAuth2UserDetails;
	}

	@Override
	public String getUsername() {
		return username;
	}

	@Override
	public boolean isAccountNonExpired() {
		return true;
	}

	@Override
	public boolean isAccountNonLocked() {
		return true;
	}

	@Override
	public boolean isCredentialsNonExpired() {
		return true;
	}

	@Override
	public boolean isEnabled() {
		return true;
	}

	@Override
	public String getName() {
		return id;
	}

}
