package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Year;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EpfContributionRequest implements Serializable {

	@NotNull
	@Digits(integer = 15, fraction = 2)
	private BigDecimal contribution;

	@NotBlank
	private String month;

	@NotNull
	private Year year;

}
