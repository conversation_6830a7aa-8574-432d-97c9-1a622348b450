package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.utilities.general.constant.RequestKeyType;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OTPVerifyRequest implements Serializable {

	@NotNull
	private RequestKeyType requestKeyType;

	@NotNull
	private UsernameType usernameType;

	@NotBlank
	private String username;

	@NotBlank
	private String key;

	@NotBlank
	private String value;

}