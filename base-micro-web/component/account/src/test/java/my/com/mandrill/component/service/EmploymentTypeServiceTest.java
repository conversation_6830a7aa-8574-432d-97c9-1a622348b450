package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.EmploymentType;
import my.com.mandrill.component.repository.jpa.EmploymentTypeRepository;
import my.com.mandrill.component.service.impl.EmploymentTypeServiceImpl;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;

@Disabled
@ExtendWith(MockitoExtension.class)
class EmploymentTypeServiceTest {

	@InjectMocks
	@Spy
	EmploymentTypeServiceImpl employmentTypeService;

	@Mock
	EmploymentTypeRepository employmentTypeRepository;

	@Mock
	UserService userService;

	@InjectMocks
	AesCryptoUtil aesCryptoUtil;

	EmploymentType employmentTypeMock;

	Optional<EmploymentType> employmentTypeOptionalMock;

	List<EmploymentType> employmentTypeListMock;

	@BeforeEach
	void setup() {
		ReflectionTestUtils.setField(aesCryptoUtil, "AES_KEY", "NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc");
		ReflectionTestUtils.setField(aesCryptoUtil, "IV_KEY", "57ad289f-3def-4c");

		employmentTypeMock = new EmploymentType();
		employmentTypeMock.setName("code");
		employmentTypeOptionalMock = Optional.of(employmentTypeMock);
		employmentTypeListMock = new ArrayList<>();
		employmentTypeListMock.add(employmentTypeMock);
	}

	@Test
	void create_Success() {
		Mockito.when(employmentTypeRepository.existsByNameAllIgnoreCase(any())).thenReturn(false);
		Mockito.when(employmentTypeRepository.save(any())).thenReturn(employmentTypeMock);
		EmploymentType obj = employmentTypeService.create(new EmploymentType());
		assertThat(obj).isEqualTo(employmentTypeMock);
	}

	@Test
	void findById_Success() {
		Mockito.when(employmentTypeRepository.findById(any())).thenReturn(employmentTypeOptionalMock);
		EmploymentType employmentType = employmentTypeService.findById("");
		assertThat(employmentType).isEqualTo(employmentTypeMock);
	}

	@Test
	void findAll_Success() {
		Page<EmploymentType> employmentTypePage = new PageImpl<>(employmentTypeListMock);
		Mockito.when(employmentTypeRepository.findAllByName(any(), any())).thenReturn(employmentTypePage);

		Page<EmploymentType> employmentTypes = employmentTypeService.findAll(Pageable.ofSize(10), "");
		assertThat(employmentTypes.getTotalElements()).isEqualTo(1);
	}

	@Test
	void update_Success() {
		Mockito.when(employmentTypeRepository.findById(any())).thenReturn(employmentTypeOptionalMock);
		Mockito.when(employmentTypeRepository.existsByNameAllIgnoreCase(any())).thenReturn(false);
		Mockito.when(employmentTypeRepository.save(any())).thenReturn(employmentTypeMock);

		EmploymentType request = new EmploymentType();
		request.setName("name");
		EmploymentType employmentType = employmentTypeService.update(request);
		assertThat(employmentType).isEqualTo(employmentTypeMock);

	}

	@Test
	void deleteById_Success() {
		Mockito.doReturn(employmentTypeMock).when(employmentTypeService).findById(any());
		Mockito.when(userService.existsByEmploymentTypeId(any())).thenReturn(false);
		Mockito.doNothing().when(employmentTypeRepository).delete(any());

		Assertions.assertDoesNotThrow(() -> employmentTypeService.deleteById(""));
	}

}
