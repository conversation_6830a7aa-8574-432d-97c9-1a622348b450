package my.com.mandrill.component.config;

import io.netty.channel.ChannelOption;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.ChatBotClient;
import my.com.mandrill.utilities.general.util.ConnectionUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

@Configuration
@RequiredArgsConstructor
public class ChatBotClientConfig {

	private final IntegrationAIProperties integrationAIProperties;

	@Bean
	public WebClient chatBotWebClient() {
		return WebClient.builder()
				.baseUrl(integrationAIProperties.getChatBotUri())
				.clientConnector(new ReactorClientHttpConnector(
						HttpClient.create(ConnectionUtil.getDefaultConnectionProvider())
								.option(ChannelOption.SO_KEEPALIVE, true)
								.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)
								.responseTimeout(Duration.ofMinutes(5)) // Longer timeout for streaming
				))
				.codecs(configurer -> {
					configurer.defaultCodecs().maxInMemorySize(1024 * 1024); // 1MB buffer
				})
				.build();
	}

	@Bean
	public ChatBotClient chatBotClient(WebClient chatBotWebClient) {
		HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
				.builder(WebClientAdapter.forClient(chatBotWebClient))
				.blockTimeout(integrationAIProperties.getChatBotTimeout()).build();
		return httpServiceProxyFactory.createClient(ChatBotClient.class);
	}

}
