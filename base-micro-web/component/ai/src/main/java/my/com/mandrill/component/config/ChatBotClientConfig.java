package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.ChatBotClient;
import my.com.mandrill.utilities.general.util.ConnectionUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.netty.http.client.HttpClient;

@Configuration
@RequiredArgsConstructor
public class ChatBotClientConfig {

	private final IntegrationAIProperties integrationAIProperties;

	@Bean
	public WebClient chatBotWebClient() {
		return WebClient.builder().baseUrl(integrationAIProperties.getChatBotUri()).clientConnector(
				new ReactorClientHttpConnector(HttpClient.create(ConnectionUtil.getDefaultConnectionProvider())))
				.build();
	}

	@Bean
	public ChatBotClient chatBotClient(WebClient chatBotWebClient) {
		HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
				.builder(WebClientAdapter.forClient(chatBotWebClient))
				.blockTimeout(integrationAIProperties.getChatBotTimeout()).build();
		return httpServiceProxyFactory.createClient(ChatBotClient.class);
	}

}
