package my.com.mandrill.component.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.request.clientRequest.ChatBotClientRequest;
import my.com.mandrill.component.dto.request.clientRequest.ChatBotFeedbackClientRequest;
import my.com.mandrill.component.service.ChatBotService;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("chat-bot")
@Slf4j
@RequiredArgsConstructor
public class ChatBotController {

	private final ChatBotService chatBotService;

	@PostMapping("chats")
	public ResponseEntity<String> sendChat(@Valid @RequestBody ChatBotClientRequest request) {

		String userId = SecurityUtil.currentUserId();
		request.setUserId(userId);

		return chatBotService.sendChat(request);
	}

	@GetMapping("chats")
	public ResponseEntity<String> getLatestChat() {

		String userId = SecurityUtil.currentUserId();

		return chatBotService.getLatestChat(userId);
	}

	@PutMapping("feedback/{sessionId}/{messageId}")
	public ResponseEntity<String> sendFeedback(@PathVariable String sessionId, @PathVariable String messageId,
			@RequestBody ChatBotFeedbackClientRequest request) {

		return chatBotService.sendFeedback(sessionId, messageId, request);

	}

	@PostMapping(value = "v2/chats", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public Flux<String> sendStreamingChat(@Valid @RequestBody ChatBotClientRequest request) {

		String userId = SecurityUtil.currentUserId();
		request.setUserId(userId);

		return chatBotService.sendStreamingChat(request);
	}

	@PostMapping(value = "stream-test", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public Flux<String> sendStreamingTest() {
		return chatBotService.sendStreamingTest();
	}

}
