package my.com.mandrill.component.dto.request.clientRequest;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductSuggestionQuestionnaireRequest {

	@JsonProperty("user_id")
	private String userId;

	@JsonProperty("selected_options")
	private Map<String, Set<String>> selectedOptions;

	private Income income;

	private Income deposit;

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class Income {

		private BigDecimal min;

		private BigDecimal max;

	}

}
