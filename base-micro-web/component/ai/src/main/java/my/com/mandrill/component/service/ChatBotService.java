package my.com.mandrill.component.service;

import my.com.mandrill.component.dto.request.clientRequest.ChatBotClientRequest;
import my.com.mandrill.component.dto.request.clientRequest.ChatBotFeedbackClientRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;

public interface ChatBotService {

	ResponseEntity<String> sendChat(ChatBotClientRequest request);

	ResponseEntity<String> getLatestChat(String userId);

	ResponseEntity<String> sendFeedback(String sessionId, String messageId, ChatBotFeedbackClientRequest request);

	Flux<ServerSentEvent<String>> sendStreamingChat();

	Flux<String> sendStreamingChatWS(ChatBotClientRequest request);

	Flux<String> sendStreamingChatFlux();
}
