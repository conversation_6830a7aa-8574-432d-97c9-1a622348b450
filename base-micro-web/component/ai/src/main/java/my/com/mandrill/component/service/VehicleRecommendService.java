package my.com.mandrill.component.service;

import my.com.mandrill.component.dto.request.clientRequest.VehicleForYouClientRequest;
import org.springframework.http.ResponseEntity;

public interface VehicleRecommendService {

	ResponseEntity<String> getVehicleList(String page, String size);

	ResponseEntity<String> getVehicleFilters();

	ResponseEntity<String> getVehicleSorting();

	ResponseEntity<String> getFilteredVehicleList(Object filter, String sort, String page, String size);

	ResponseEntity<String> addVehicleToFavourites(String vehicleId, boolean like);

	ResponseEntity<String> applyVehicle(String vehicleId, boolean apply);

	ResponseEntity<String> compareVehicle(String vehicleId, boolean compare);

	ResponseEntity<String> getVehicleDescription(String vehicleId);

	ResponseEntity<String> getVehicleVariants(String vehicleId);

	ResponseEntity<String> getCompareList();

	ResponseEntity<String> getCompareCount();

	ResponseEntity<String> getFavouriteList();

	ResponseEntity<String> getMinInstallmentVehicle();

	ResponseEntity<String> getVehicleForYou(VehicleForYouClientRequest vehicleForYouClientRequest, String sort,
			String page, String size);

}
