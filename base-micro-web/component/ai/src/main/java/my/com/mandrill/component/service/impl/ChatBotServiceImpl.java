package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.client.ChatBotClient;
import my.com.mandrill.component.dto.request.clientRequest.ChatBotClientRequest;
import my.com.mandrill.component.dto.request.clientRequest.ChatBotFeedbackClientRequest;
import my.com.mandrill.component.service.ChatBotService;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChatBotServiceImpl implements ChatBotService {

	private final ChatBotClient chatBotClient;

	private final ProxyFeignClient proxyFeignClient;

	@Override
	public ResponseEntity<String> sendChat(ChatBotClientRequest request) {

		return chatBotClient.sendChat(request);
	}

	@Override
	public ResponseEntity<String> getLatestChat(String userId) {

		return chatBotClient.getLatestChat(userId);
	}

	@Override
	public ResponseEntity<String> sendFeedback(String sessionId, String messageId,
			ChatBotFeedbackClientRequest request) {
		return chatBotClient.sendFeedback(sessionId, messageId, request);
	}

	@Override
	public Flux<ServerSentEvent<String>> sendStreamingChat() {

		log.info("send streaming chat");

		return chatBotClient.sendStreamingChatTest();
	}

	@Override
	public Flux<String> sendStreamingChatWS(ChatBotClientRequest request) {
		return chatBotClient.sendStreamingChat(request);
	}

}