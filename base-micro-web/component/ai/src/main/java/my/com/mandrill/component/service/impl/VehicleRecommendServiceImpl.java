package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.client.VehicleRecommendClient;
import my.com.mandrill.component.dto.model.UserApplyVehicleDTO;
import my.com.mandrill.component.dto.model.UserCompareVehicleDTO;
import my.com.mandrill.component.dto.model.UserFavouriteVehicleDTO;
import my.com.mandrill.component.dto.request.clientRequest.VehicleForYouClientRequest;
import my.com.mandrill.component.service.VehicleRecommendService;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleRecommendServiceImpl implements VehicleRecommendService {

	private final VehicleRecommendClient vehicleRecommendClient;

	@Override
	public ResponseEntity<String> getVehicleList(String page, String size) {
		return vehicleRecommendClient.getVehicleList(SecurityUtil.currentUserId(), page, size);
	}

	@Override
	public ResponseEntity<String> getVehicleFilters() {
		return vehicleRecommendClient.getVehicleFilters();
	}

	@Override
	public ResponseEntity<String> getVehicleSorting() {
		return vehicleRecommendClient.getVehicleSorting();
	}

	@Override
	public ResponseEntity<String> getFilteredVehicleList(Object filter, String sort, String page, String size) {
		return vehicleRecommendClient.getFilteredVehicleList(SecurityUtil.currentUserId(), filter, sort, page, size);
	}

	@Override
	public ResponseEntity<String> addVehicleToFavourites(String vehicleId, boolean like) {
		UserFavouriteVehicleDTO userFavouriteVehicleDTO = new UserFavouriteVehicleDTO();
		userFavouriteVehicleDTO.setVehicle_id(vehicleId);
		userFavouriteVehicleDTO.setUser_id(SecurityUtil.currentUserId());
		userFavouriteVehicleDTO.setSwipe_interest(like);
		return vehicleRecommendClient.addVehicleToFavourites(userFavouriteVehicleDTO);
	}

	@Override
	public ResponseEntity<String> applyVehicle(String vehicleId, boolean apply) {
		UserApplyVehicleDTO userApplyVehicleDTO = new UserApplyVehicleDTO();
		userApplyVehicleDTO.setVehicle_id(vehicleId);
		userApplyVehicleDTO.setUser_id(SecurityUtil.currentUserId());
		userApplyVehicleDTO.setApplied(apply);
		return vehicleRecommendClient.applyVehicle(userApplyVehicleDTO);
	}

	@Override
	public ResponseEntity<String> compareVehicle(String vehicleId, boolean compare) {
		UserCompareVehicleDTO userCompareVehicleDTO = new UserCompareVehicleDTO();
		userCompareVehicleDTO.setVehicle_id(vehicleId);
		userCompareVehicleDTO.setUser_id(SecurityUtil.currentUserId());
		userCompareVehicleDTO.setCompare(compare);
		return vehicleRecommendClient.compareVehicle(userCompareVehicleDTO);
	}

	@Override
	public ResponseEntity<String> getVehicleDescription(String vehicleId) {
		return vehicleRecommendClient.getVehicleDescription(vehicleId);
	}

	@Override
	public ResponseEntity<String> getVehicleVariants(String vehicleId) {
		return vehicleRecommendClient.getVehicleVariants(vehicleId, SecurityUtil.currentUserId());
	}

	@Override
	public ResponseEntity<String> getCompareList() {
		return vehicleRecommendClient.getCompareList(SecurityUtil.currentUserId());
	}

	@Override
	public ResponseEntity<String> getCompareCount() {
		return vehicleRecommendClient.getCompareCount(SecurityUtil.currentUserId());
	}

	@Override
	public ResponseEntity<String> getFavouriteList() {
		return vehicleRecommendClient.getFavouriteList(SecurityUtil.currentUserId());
	}

	@Override
	public ResponseEntity<String> getMinInstallmentVehicle() {
		return vehicleRecommendClient.getMinMonthlyInstallmentVehicle();
	}

	@Override
	public ResponseEntity<String> getVehicleForYou(VehicleForYouClientRequest vehicleForYouClientRequest, String sort,
			String page, String size) {
		return vehicleRecommendClient.getVehicleForYou(vehicleForYouClientRequest, sort, page, size);
	}

}