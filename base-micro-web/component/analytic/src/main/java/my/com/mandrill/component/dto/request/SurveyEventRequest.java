package my.com.mandrill.component.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.NPSResultEnum;
import my.com.mandrill.utilities.general.constant.SurveyAction;
import my.com.mandrill.utilities.general.constant.SurveyFormType;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurveyEventRequest implements Serializable {

	private String userId;

	private SurveyAction action;

	private NPSResultEnum result;

	private LocalDate surveyDate;

	private SurveyFormType formType;

}
