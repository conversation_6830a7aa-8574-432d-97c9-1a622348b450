package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.SocialMediaConversion;
import my.com.mandrill.component.dto.request.UserInterestedRedirectForLoggedInUserRequest;
import my.com.mandrill.component.repository.SocialMediaConversionParamRepository;
import my.com.mandrill.component.repository.SocialMediaConversionRepository;
import my.com.mandrill.component.service.EmailService;
import my.com.mandrill.component.service.SocialMediaConversionService;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.request.EmailRequest;
import my.com.mandrill.utilities.general.constant.EmailTemplateVariableEnum;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.TemplateNameEnum;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Map;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {

	public static final String FAIL_SENDING_EMAIL_VIA_SERVICE = "fail sending email via service: ";

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final ObjectMapper objectMapper;

	@Override
	public void sendEmail(CurrentUserIdDTO userDTO, String template) {
		Map<String, Object> templateVariable = Map.of(EmailTemplateVariableEnum.FIRST_NAME.getValue(),
				userDTO.getFullName());

		EmailRequest emailRequest = EmailRequest.builder().to(Collections.singletonList(userDTO.getEmail()))
				.templateVariable(templateVariable).templateName(template).build();

		try {
			kafkaTemplate.send(KafkaTopic.SEND_EMAIL_TOPIC, userDTO.getEmail(),
					objectMapper.writeValueAsString(emailRequest));
		}
		catch (Exception e) {
			log.error(FAIL_SENDING_EMAIL_VIA_SERVICE + emailRequest.getTo());
		}
	}

}
