package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.SurveyForm;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.SurveyFormRepository;
import my.com.mandrill.component.service.SurveyFormService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SurveyFormServiceImpl implements SurveyFormService {

	private final SurveyFormRepository surveyFormRepository;

	@Override
	public SurveyForm getSurveyFormById(String formId) {
		return surveyFormRepository.findById(formId)
				.orElseThrow(() -> new BusinessException(ErrorCodeEnum.SURVEY_FORM_NOT_FOUND));
	}

}
