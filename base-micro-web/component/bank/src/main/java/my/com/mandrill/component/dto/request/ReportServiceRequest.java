package my.com.mandrill.component.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.LeadManagementDateType;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportServiceRequest {

	private String refNo;

	private String userRefNo;

	private LeadManagementDateType dateType;

	private LocalDate startDate;

	private LocalDate endDate;

	private String fullName;

	private String productType;

	private String productName;

	private Set<String> productIds;

	private Pageable pageable;

	private Set<String> issuerCodes;

	private Set<String> providerIds;

	private Boolean isRedirect;

	private List<RSMRelationType> rsmRelation;

	private List<String> productTypes;

	private List<RSMStatus> rsmStatus;

	private List<Boolean> rsmCommissionAttached;

	private List<Boolean> rsmEligible;

}
