package my.com.mandrill.component.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.LoanEligibilityCommitmentType;
import my.com.mandrill.component.constant.LoanEligibilityIncomeType;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.LoanEligibilityUserData;
import my.com.mandrill.component.dto.request.LoanEligibilityApplicantRequest;
import my.com.mandrill.component.dto.request.LoanEligibilityRequest;
import my.com.mandrill.component.dto.response.ProductSuggestedValueResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.LoanUtil;
import my.com.mandrill.utilities.feign.dto.IncomeDTO;
import my.com.mandrill.utilities.feign.dto.PropertyDTO;
import my.com.mandrill.utilities.feign.dto.model.LoanPlusReportDTO;
import my.com.mandrill.utilities.feign.dto.model.ScreenCommitmentStructureDTO;
import my.com.mandrill.utilities.feign.dto.model.ScreenIncomeStructureDTO;
import my.com.mandrill.utilities.feign.dto.request.STPBankerAssigmentRequest;
import my.com.mandrill.utilities.feign.dto.request.ScreenInternalRequest;
import my.com.mandrill.utilities.feign.dto.response.PreApprovalResponse;
import my.com.mandrill.utilities.feign.dto.response.STPBankerAssigmentResponse;
import my.com.mandrill.utilities.feign.dto.response.ScreenResponse;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.LoanTypeEnum;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.dto.request.ObjectRequest;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.PhoneNumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class LoanEligibilityIntegrationServiceImpl implements LoanEligibilityIntegrationService {

	public static final String LOAN_PLUS_READY_FOR_BANKER_ASSIGNMENT_STATUS = "Ready For Banker Assignment";

	private final BankListService bankListService;

	private final LoanEligibilityService loanEligibilityService;

	private final LoanService loanService;

	private final ProxyFeignClient proxyFeignClient;

	private final BankIntegrationService bankIntegrationService;

	private final LoanEligibilityLenderService loanEligibilityLenderService;

	@Override
	public void validateApplicant(LoanEligibilityApplicantRequest request) {
		proxyFeignClient.getAccountFeignClient().findIdTypeById(request.getIdType().getId());

		PhoneNumberUtil.ExtractedPhoneNumber phoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(request.getPhoneCountry() + request.getPhoneNumber());
		request.setPhoneCountry(phoneNumber.getPhoneCountry());
		request.setPhoneNumber(phoneNumber.getPhoneNumber());

		if (Period.between(request.getDob(), LocalDate.now(TimeConstant.DEFAULT_ZONE_ID)).getYears() < 1) {
			throw new BusinessException(ErrorCodeEnum.APPLICANT_MUST_AT_LEAST_ONE_YEAR_OLD);
		}
	}

	@Override
	public LoanEligibilityUserData getUserData(String userId) {
		// User Data Preparation
		List<IncomeDTO> myIncomes = proxyFeignClient.getAccountFeignClient().findByUserAndIncomeType(null,
				Sort.unsorted());

		List<PropertyDTO> properties = proxyFeignClient.getPropertyFeignClient()
				.getPropertyByUserIdAndIsRentedAndIsBrowsingFalse(true);

		List<Loan> loans = loanService.findByUserIdAndTypeIn(userId,
				List.of(LoanTypeEnum.HOME_LOANS, LoanTypeEnum.AUTO_LOANS, LoanTypeEnum.PERSONAL_LOANS));

		// Calculate Gross Salary
		BigDecimal grossSalary = LoanUtil.calculateGrossSalary(myIncomes);

		// Calculate MTD
		BigDecimal mtd = proxyFeignClient.getCommonFeignClient().calculateMtdGivenGrossMonthlySalary(grossSalary)
				.getValue();

		// Calculate SelfEmployedIncome
		BigDecimal selfEmployedIncome = LoanUtil.calculateSelfEmployedIncome(myIncomes);

		// Calculate Rental Income
		BigDecimal rentalIncome = LoanUtil.calculateRentalIncome(properties);

		// Calculate Loans
		BigDecimal mortgageLoan = LoanUtil.calculateLoanByType(loans, LoanTypeEnum.HOME_LOANS);
		BigDecimal autoLoan = LoanUtil.calculateLoanByType(loans, LoanTypeEnum.AUTO_LOANS);
		BigDecimal personalLoan = LoanUtil.calculateLoanByType(loans, LoanTypeEnum.PERSONAL_LOANS);

		LoanEligibilityUserData userData = LoanEligibilityUserData.builder().grossSalary(grossSalary).mtd(mtd)
				.selfEmployedIncome(selfEmployedIncome).rentalIncome(rentalIncome).mortgageLoan(mortgageLoan)
				.autoLoan(autoLoan).personalLoan(personalLoan).build();

		log.debug("User Data: {}", userData);
		return userData;
	}

	@Override
	public void validateNewApplicant(LoanEligibilityRequest request) {

		if (StringUtils.isNotBlank(request.getEntityId())) {
			if (request.getEntityName().equals(EntityName.PROPERTY)) {
				proxyFeignClient.getPropertyFeignClient().existsById(request.getEntityId());
			}
			else {
				throw new BusinessException(ErrorCodeEnum.LOAN_ELIGIBILITY_NOT_SUPPORTED,
						request.getEntityName().name());
			}
		}

		validateApplicant(request);
	}

	// TODO: improve this. The property feign client endpoint is calling bank service
	// which causing the performance issue. Currently this is not urgent. So skip this
	// first
	@Override
	public ProductSuggestedValueResponse productSuggestedValue(String id, String userId) {
		LoanEligibility loanEligibility = loanEligibilityService.findById(id, userId);
		ProductSuggestedValueResponse suggestedValue = new ProductSuggestedValueResponse();
		if (StringUtils.isNotBlank(loanEligibility.getEntityId())) {

			if (loanEligibility.getEntityName() == EntityName.PROPERTY) {
				PropertyDTO propertyDTO = proxyFeignClient.getPropertyFeignClient()
						.findById(loanEligibility.getEntityId());
				if (propertyDTO.getAverageMarketValue() != null) {
					suggestedValue.setPurchasePrice(propertyDTO.getAverageMarketValue());
				}
				else if (propertyDTO.getPurchaseValue() != null) {
					suggestedValue.setPurchasePrice(propertyDTO.getPurchaseValue());
				}

				loanService
						.findOptionalByEntityNameAndEntityId(loanEligibility.getEntityName(),
								loanEligibility.getEntityId(), userId)
						.ifPresent(value -> suggestedValue
								.setRemainingLoanAmount(loanService.calculateLoanEndingBalance(value)));
			}
			else {
				throw new BusinessException(ErrorCodeEnum.LOAN_ELIGIBILITY_NOT_SUPPORTED,
						loanEligibility.getEntityName().name());
			}

		}
		return suggestedValue;
	}

	@Override
	public LoanEligibility caseCreation(String id, String userId) {
		LoanEligibility loanEligibility = loanEligibilityService.findById(id, userId);

		LoanUtil.checkCommitmentsOverIncomes(loanEligibility.getCommitments(), loanEligibility.getIncomes());
		if (StringUtils.isBlank(loanEligibility.getCaseCode())) {
			if (loanEligibility.getApplicant() == null || loanEligibility.getProduct() == null) {
				throw new BusinessException(ErrorCodeEnum.CASE_CREATION_REQUIRED_FLOW_MISSING);
			}
			// call finology
			ScreenInternalRequest request = new ScreenInternalRequest();
			request.setProductCode(loanEligibility.getProductCode());
			request.setRefCode(loanEligibility.getRefNo());

			// build applicant
			ScreenInternalRequest.ScreenRequestApplicant applicant = new ScreenInternalRequest.ScreenRequestApplicant();
			request.setApplicant(applicant);

			applicant.setDob(loanEligibility.getApplicant().getDob());
			applicant.setEmail(loanEligibility.getApplicant().getEmail());
			applicant.setIcOrPassportNo(loanEligibility.getApplicant().getIdNumber());
			applicant.setName(loanEligibility.getApplicant().getFullName());
			applicant.setPhone(PhoneNumberUtil.formatPhoneNumber(loanEligibility.getApplicant().getPhoneCountry(),
					loanEligibility.getApplicant().getPhoneNumber()));

			// build income object
			Map<LoanEligibilityIncomeType, LoanEligibilityIncome> incomeMap = loanEligibility.getIncomes().stream()
					.collect(Collectors.toMap(LoanEligibilityIncome::getType, x -> x));
			ScreenInternalRequest.ScreenRequestIncome income = new ScreenInternalRequest.ScreenRequestIncome();
			applicant.setIncomes(income);

			income.setAsbIncome(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.ASB)));
			income.setCommission(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.COMMISSION)));
			income.setContractualBonus(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.CONTRACTUAL_BONUS)));
			income.setGrossFixedAllowance(
					buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.GROSS_FIXED_ALLOWANCE)));
			income.setGrossSalary(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.GROSS_SALARY)));
			income.setInterestDepositIncome(
					buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.INTEREST_DEPOSIT)));
			income.setOvertime(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.OVERTIME)));
			income.setPerformanceBonus(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.PERFORMANCE_BONUS)));
			income.setRentalIncome(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.RENTAL)));
			income.setSelfEmployedIncome(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.SELF_EMPLOYED)));

			// build commitment object
			Map<LoanEligibilityCommitmentType, LoanEligibilityCommitment> commitmentMap = loanEligibility
					.getCommitments().stream().collect(Collectors.toMap(LoanEligibilityCommitment::getType, x -> x));
			ScreenInternalRequest.ScreenRequestCommitments commitments = new ScreenInternalRequest.ScreenRequestCommitments();
			applicant.setCommitments(commitments);

			commitments.setAsbLoan(buildLoanPlusCommitment(commitmentMap.get(LoanEligibilityCommitmentType.ASB_LOAN)));
			commitments.setCreditCard(
					buildLoanPlusCommitment(commitmentMap.get(LoanEligibilityCommitmentType.CREDIT_CARD)));
			commitments.setHirePurchase(
					buildLoanPlusCommitment(commitmentMap.get(LoanEligibilityCommitmentType.HIRE_PURCHASE)));
			commitments.setMortgageLoan(
					buildLoanPlusCommitment(commitmentMap.get(LoanEligibilityCommitmentType.MORTGAGE_LOAN)));
			commitments
					.setOtherLoan(buildLoanPlusCommitment(commitmentMap.get(LoanEligibilityCommitmentType.OTHER_LOAN)));
			commitments
					.setOverdraft(buildLoanPlusCommitment(commitmentMap.get(LoanEligibilityCommitmentType.OVERDRAFT)));
			commitments.setPersonalLoan(
					buildLoanPlusCommitment(commitmentMap.get(LoanEligibilityCommitmentType.PERSONAL_LOAN)));

			// build deduction object
			ScreenInternalRequest.ScreenRequestDeductions deductions = new ScreenInternalRequest.ScreenRequestDeductions();
			applicant.setStatutoryDeductions(deductions);
			deductions.setPcb(buildLoanPlusIncome(incomeMap.get(LoanEligibilityIncomeType.PCB)));

			// build product
			ScreenInternalRequest.ScreenRequestProductDetails productDetails = new ScreenInternalRequest.ScreenRequestProductDetails();
			request.setProductDetails(productDetails);

			productDetails.setLoanPurpose(loanEligibility.getProduct().getLoanPurpose().getLoanPlusCode());
			productDetails.setMarginOfFinance(loanEligibility.getProduct().getMarginOfFinance());
			productDetails.setPurchasePrice(loanEligibility.getProduct().getPurchasePrice());
			productDetails.setRefinanceAmount(loanEligibility.getProduct().getRefinanceAmount());
			productDetails.setRemainingLoanAmount(loanEligibility.getProduct().getRemainingLoanAmount());
			productDetails.setTenure(loanEligibility.getProduct().getTenure());

			ScreenResponse response = proxyFeignClient.getFinologyFeignClient().caseCreationScreening(request);

			if (response.getData() != null) {
				loanEligibility.setCaseCode(response.getData().getCaseCode());
				loanEligibility.setCaseCreationTransactionId(response.getTransactionId());
				loanEligibility.setStatus(response.getData().getStatus());
				loanEligibility = loanEligibilityService.save(loanEligibility);
			}
			else {
				throw new BusinessException(ErrorCodeEnum.FAILED_TO_CREATE_CASE);
			}

		}
		return loanEligibility;
	}

	@Override
	public LoanEligibility preApproval(String id, String userId) {
		LoanEligibility loanEligibility = loanEligibilityService.findById(id, userId);

		validateCaseCreation(loanEligibility);

		if (loanEligibility.getLenders().isEmpty()) {
			PreApprovalResponse response = proxyFeignClient.getFinologyFeignClient()
					.preApproval(loanEligibility.getCaseCode());

			// map lender
			if (response.getData() != null) {

				// Lenders
				response.getData().getBestMatchingBankers().forEach(bestMatchingBanker -> {
					Optional<BankList> bankList = bankListService
							.findOptionalByFinologyCode(bestMatchingBanker.getCode());

					if (bankList.isPresent()) {
						LoanEligibilityLender lender = MapStructConverter.MAPPER
								.toLoanEligibilityLender(bestMatchingBanker);
						lender.setLoanEligibility(loanEligibility);
						lender.setProvider(bankList.get());

						loanEligibility.getLenders().add(lender);
					}
				});

				// DSR Before Loan
				if (response.getData().getMainApplication() != null
						&& response.getData().getMainApplication().getRating() != null) {
					loanEligibility
							.setDsrPercentageBeforeLoan(response.getData().getMainApplication().getRating().getDsr());
				}

				// DSR After Loan
				if (response.getData().getOverallRating() != null) {
					PreApprovalResponse.OverallRating overallRating = response.getData().getOverallRating();

					loanEligibility.setDsrPercentageAfterLoan(overallRating.getDsrPercentage());
					loanEligibility.setDsrRatingAfterLoan(overallRating.getDsr());
				}

				// Apply Date
				loanEligibility.setApplyDate(response.getData().getApplyDate());

				loanEligibility.setPreApprovalTransactionId(response.getTransactionId());
			}

		}

		return loanEligibilityService.save(loanEligibility);
	}

	@Override
	public LoanEligibility lenderAssignment(String id, String userId, ObjectRequest lender) {
		LoanEligibility loanEligibility = loanEligibilityService.findById(id, userId);

		validateCaseCreation(loanEligibility);

		if (StringUtils.equalsIgnoreCase(loanEligibility.getStatus(), LOAN_PLUS_READY_FOR_BANKER_ASSIGNMENT_STATUS)) {
			Optional<LoanEligibilityLender> matchedLender = loanEligibility.getLenders().stream()
					.filter(loanEligibilityLender -> loanEligibilityLender.getId().equals(lender.getId())).findFirst();
			if (matchedLender.isPresent()) {
				LoanEligibilityLender loanEligibilityLender = matchedLender.get();
				// Logic to make sure calling banker assignment status not failed
				if (Boolean.TRUE.equals(loanEligibilityLender.getIsStp())
						&& Boolean.FALSE.equals(loanEligibilityLender.getBankerIsAssigned())
						&& loanEligibilityLender.getLenderId() != null) {

					STPBankerAssigmentRequest request = new STPBankerAssigmentRequest(loanEligibility.getRefNo(),
							loanEligibility.getCaseCode(), List.of(loanEligibilityLender.getLenderId()));
					STPBankerAssigmentResponse response = proxyFeignClient.getFinologyFeignClient()
							.stpBankerAssignment(request);

					Optional<STPBankerAssigmentResponse.STPBankerAssigmentData> matchedBanker = response.getData()
							.stream().filter(returnedData -> loanEligibilityLender.getLenderId()
									.equals(returnedData.getLenderId()))
							.findFirst();

					if (matchedBanker.isPresent()) {
						loanEligibilityLender.setBankerAssignmentStatus(matchedBanker.get().getAssignmentStatus());
						loanEligibilityLender.setBankerAssignedAt(matchedBanker.get().getAssignedAt());
						loanEligibilityLender.setBankerIsAssigned(true);
						loanEligibility.setStpBankersAssignmentTransactionId(response.getTransactionId());
					}
					else {
						// if not found means, loan plus does not return any data
						throw new BusinessException(ErrorCodeEnum.FAILED_TO_ASSIGN_LENDER);
					}
				}
				else {
					log.warn("Lender {} isStp: {}, bankerIsAssigned: {}, lenderId: {}", loanEligibilityLender.getId(),
							loanEligibilityLender.getIsStp(), loanEligibilityLender.getBankerIsAssigned(),
							loanEligibilityLender.getLenderId());
				}
			}
			else {
				throw ExceptionPredicate.loanEligibilityLenderNotFound(lender.getId()).get();
			}

		}
		else {
			throw new BusinessException(ErrorCodeEnum.CASE_NOT_READY_FOR_BANK_ASSIGNMENT);
		}
		return loanEligibilityService.save(loanEligibility);
	}

	private ScreenIncomeStructureDTO buildLoanPlusIncome(LoanEligibilityIncome income) {
		if (income == null)
			return null;

		ScreenIncomeStructureDTO incomeStructureDTO = new ScreenIncomeStructureDTO();
		incomeStructureDTO.setValue(income.getValue());
		incomeStructureDTO.setCurrencyCode(income.getCurrency().name());
		return incomeStructureDTO;
	}

	private List<ScreenCommitmentStructureDTO> buildLoanPlusCommitment(LoanEligibilityCommitment commitment) {
		if (commitment == null)
			return List.of();

		ScreenCommitmentStructureDTO commitmentStructureDTO = new ScreenCommitmentStructureDTO();
		commitmentStructureDTO.setMonthlyInstalment(commitment.getMonthlyInstalment());
		commitmentStructureDTO.setCurrencyCode(commitment.getCurrency().name());
		return List.of(commitmentStructureDTO);
	}

	private void validateCaseCreation(LoanEligibility loanEligibility) {
		if (StringUtils.isBlank(loanEligibility.getCaseCode())) {
			throw new BusinessException(ErrorCodeEnum.CASE_CREATION_NOT_INITIATED);
		}
	}

	@Override
	public List<LoanPlusReportDTO> exportReportData(LocalDate startDate, LocalDate endDate) {
		List<LoanEligibilityLender> results = loanEligibilityLenderService
				.findByBankerIsAssignedTrueAndCreatedDateWithRange(startDate, endDate);

		return results.stream().map(this::mapToDTO).collect(Collectors.toList());

	}

	@Override
	public Page<LoanPlusReportDTO> exportReportData(LocalDate startDate, LocalDate endDate, Pageable pageable) {
		Page<LoanEligibilityLender> results = loanEligibilityLenderService
				.findByBankerIsAssignedTrueAndCreatedDateWithRange(startDate, endDate, pageable);

		return results.map(this::mapToDTO);

	}

	private LoanPlusReportDTO mapToDTO(LoanEligibilityLender loanEligibilityLender) {
		LoanEligibilityApplicant applicant = loanEligibilityLender.getLoanEligibility().getApplicant();

		LoanPlusReportDTO loanPlusReportDTO = new LoanPlusReportDTO();
		loanPlusReportDTO.setCreatedDate(loanEligibilityLender.getCreatedDate());
		loanPlusReportDTO.setFullName(applicant.getFullName());
		loanPlusReportDTO.setPhoneNumber(applicant.getPhoneCountry() + applicant.getPhoneNumber());
		loanPlusReportDTO.setNric(applicant.getIdNumber());
		loanPlusReportDTO.setEmail(applicant.getEmail());
		loanPlusReportDTO.setCaseCode(loanEligibilityLender.getLoanEligibility().getCaseCode());
		loanPlusReportDTO.setLenderName(loanEligibilityLender.getProvider().getName());
		loanPlusReportDTO.setLenderCode(loanEligibilityLender.getCode());
		loanPlusReportDTO.setLoanPurpose(
				loanEligibilityLender.getLoanEligibility().getProduct().getLoanPurpose().getLoanPlusCode());
		loanPlusReportDTO.setMaxTenure(loanEligibilityLender.getLoanEligibility().getProduct().getTenure());
		loanPlusReportDTO.setMaxLoanAmount(loanEligibilityLender.getMaxLoanAmount());
		loanPlusReportDTO.setEstimatedInstalment(loanEligibilityLender.getMaxNewMonthlyCommitment());
		loanPlusReportDTO.setInterestRate(loanEligibilityLender.getIndicativeInterestRate());
		return loanPlusReportDTO;
	}

}
