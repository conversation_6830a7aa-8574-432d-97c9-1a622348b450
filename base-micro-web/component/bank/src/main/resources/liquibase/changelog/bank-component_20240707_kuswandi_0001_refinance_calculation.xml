<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="bank-component_20240707_kuswandi_0001" author="kuswandi">
        <createTable tableName="loan_refinance_calculation">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loan_refinance_calculation"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="monthly_difference" type="DOUBLE" />
            <column name="interest_difference" type="DOUBLE" />
            <column name="cash_out" type="DOUBLE" />
            <column name="current_loan_type" type="VARCHAR(50)" />
            <column name="current_outstanding_loan_amount" type="DOUBLE" />
            <column name="remaining_tenure" type="DECIMAL" />
            <column name="current_interest_rate" type="DOUBLE" />
            <column name="current_monthly_installment" type="DOUBLE" />
            <column name="refinance_outstanding_loan_amount" type="DOUBLE" />
            <column name="refinance_outstanding_loan_amount_diff_by" type="DOUBLE" />
            <column name="refinance_loan_tenure" type="DOUBLE" />
            <column name="refinance_interest_rate" type="DOUBLE" />
            <column name="refinance_monthly_installment" type="DOUBLE" />
            <column name="refinance_cost" type="DOUBLE" />
        </createTable>
    </changeSet>
</databaseChangeLog>