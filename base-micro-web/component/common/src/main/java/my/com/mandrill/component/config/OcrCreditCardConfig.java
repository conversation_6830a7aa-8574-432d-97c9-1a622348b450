package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.OcrCreditCardClient;
import my.com.mandrill.utilities.general.util.ConnectionUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.netty.http.client.HttpClient;

@Configuration
@RequiredArgsConstructor
public class OcrCreditCardConfig {

	private final IntegrationAIProperties properties;

	@Bean
	public WebClient ocrCreditCardWebClient() {
		return WebClient.builder().baseUrl(properties.getOcrCreditCardUri()).clientConnector(
				new ReactorClientHttpConnector(HttpClient.create(ConnectionUtil.getDefaultConnectionProvider())))
				.build();
	}

	@Bean
	public OcrCreditCardClient ocrCreditCardClient(WebClient ocrCreditCardWebClient) {
		return HttpServiceProxyFactory.builder(WebClientAdapter.forClient(ocrCreditCardWebClient))
				.blockTimeout(properties.getOcrCreditCardTimeout()).build().createClient(OcrCreditCardClient.class);
	}

}
