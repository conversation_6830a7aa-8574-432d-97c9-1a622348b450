package my.com.mandrill.component.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.utilities.general.service.SchedulerDataConfig;

@Getter
@AllArgsConstructor
public enum SchedulerDataInit implements SchedulerDataConfig {

	CHECKER_TEMPORARY_VAULT(KafkaTopicConfig.CHECKER_TEMPORARY_VAULT, KafkaTopicConfig.GROUP,
			KafkaTopicConfig.CHECKER_TEMPORARY_VAULT, "0 0/15 * 1/1 * ? *");

	private final String destination;

	private final String jobGroup;

	private final String jobName;

	private final String cronExpression;

}
