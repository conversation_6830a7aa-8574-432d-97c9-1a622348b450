package my.com.mandrill.component.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constants.IssuerTypeEnum;
import my.com.mandrill.component.domain.ProductGroup;
import my.com.mandrill.component.domain.ProductPlatform;
import my.com.mandrill.component.domain.ProductType;
import my.com.mandrill.component.dto.model.AIError;
import my.com.mandrill.component.dto.model.ProductGroupDTO;
import my.com.mandrill.component.dto.model.ProductPlatformDTO;
import my.com.mandrill.component.dto.model.ProductTypeDTO;
import my.com.mandrill.component.dto.request.AnswerRequest;
import my.com.mandrill.component.dto.request.ProductLoanLimitRequest;
import my.com.mandrill.component.dto.request.ProductSuggestionRequest;
import my.com.mandrill.component.dto.request.SuggestionLoanLimitRequest;
import my.com.mandrill.component.dto.response.PartnerResponse;
import my.com.mandrill.component.exception.AIIntegrationException;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.IntegrationAIService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.client.BankFeignClient;
import my.com.mandrill.utilities.feign.client.InsuranceFeignClient;
import my.com.mandrill.utilities.feign.dto.ProviderDTO;
import my.com.mandrill.utilities.general.dto.BankListDTO;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("ai")
@Slf4j
@RequiredArgsConstructor
public class IntegrationAIController {

	private final IntegrationAIService integrationAIService;

	private final BankFeignClient bankFeignClient;

	private final InsuranceFeignClient insuranceFeignClient;

	private final ValidationService validationService;

	private final ObjectMapper objectMapper;

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@GetMapping("product-list/{type}")
	public ResponseEntity<String> productList(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type) {

		return integrationAIService.findProductList(page, size, type);
	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@GetMapping("product-filters/{type}")
	public ResponseEntity<String> productFiltersList(@PathVariable String type,
			@RequestParam(required = false) String recommendationType) {
		return integrationAIService.findProductFilters(type, recommendationType);
	}

	/**
	 * @deprecated since PRJA-1628
	 */
	@Deprecated(since = "PRJA-1628")
	@PostMapping("product-suggestions/{type}")
	public ResponseEntity<String> productSuggestions(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @RequestParam(required = false) String userJourneyId,
			@RequestParam(required = false) String group, @PathVariable String type,
			@RequestBody ProductSuggestionRequest body) {

		return integrationAIService.findProductSuggestion(page, size, userJourneyId, group, type, body);
	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@GetMapping("product-details/{type}")
	public ResponseEntity<String> productDetailsList(@PathVariable String type, @RequestParam String productName) {

		return integrationAIService.findProductDetails(type, productName);
	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@GetMapping("product-compare/{type}")
	public ResponseEntity<String> productCompare(@PathVariable String type, @RequestParam String productName1,
			@RequestParam String productName2) {

		return integrationAIService.findProductCompare(type, productName1, productName2);
	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@GetMapping("product-category/{group}")
	public ResponseEntity<String> productCategory(@PathVariable String group) {

		return integrationAIService.findProductCategory(group);
	}

	/**
	 * @deprecated since PRJA-1628
	 */
	@Deprecated(since = "PRJA-1628")
	@GetMapping("product-for-you")
	public ResponseEntity<String> productForYou(@RequestParam(required = false) String size,
			@RequestParam String userJourneyId) {

		return integrationAIService.findProductForYou(size, userJourneyId);
	}

	/**
	 * @deprecated since PRJA-1536
	 */
	@Deprecated(since = "PRJA-1536")
	@PostMapping(value = "prediction", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
	public ResponseEntity<String> prediction(@RequestPart MultipartFile image, @RequestParam String type)
			throws JsonProcessingException {

		ResponseEntity<String> response = integrationAIService.predictOcr(image, type);

		if (StringUtils.isNotBlank(response.getBody())) {
			AIError aiError = objectMapper.readValue(response.getBody(), AIError.class);

			if (aiError == null) {
				throw new BusinessException(ErrorCodeEnum.AI_INTEGRATION_FAILED_ERROR);
			}
			else if (StringUtils.isNotBlank(aiError.getError())) {
				log.error("Prediction Error: [{}]", aiError);
				throw new AIIntegrationException(aiError);
			}
			return response;
		}
		else {
			throw new BusinessException(ErrorCodeEnum.AI_INTEGRATION_FAILED_ERROR);
		}

	}

	/**
	 * @deprecated since PRJA-1520
	 */
	@Deprecated(since = "PRJA-1520")
	@PostMapping(value = "prediction/credit-card", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
	public ResponseEntity<String> predictionCreditCard(@RequestPart MultipartFile image)
			throws JsonProcessingException {

		ResponseEntity<String> response = integrationAIService.predictOcrCreditCard(image);

		if (StringUtils.isNotBlank(response.getBody())) {
			AIError aiError = objectMapper.readValue(response.getBody(), AIError.class);

			if (aiError == null) {
				throw new BusinessException(ErrorCodeEnum.AI_INTEGRATION_FAILED_ERROR);
			}
			else if (StringUtils.isNotBlank(aiError.getError())) {
				log.error("Prediction Error: [{}]", aiError);
				throw new AIIntegrationException(aiError);
			}
			return response;
		}
		else {
			throw new BusinessException(ErrorCodeEnum.AI_INTEGRATION_FAILED_ERROR);
		}

	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@GetMapping("product/{type}")
	public ResponseEntity<String> product(@PathVariable String type, @RequestParam(required = false) String entityName,
			@RequestParam(required = false) String cardType, @RequestParam(required = false) String productName,
			@RequestParam(required = false) String productId) {

		return integrationAIService.findProduct(type, entityName, cardType, productName, productId);
	}

	/**
	 * @deprecated since PRJA-1628
	 */
	@Deprecated(since = "PRJA-1628")
	@GetMapping("product/groups")
	public ResponseEntity<List<ProductGroupDTO>> getAllProductGroup(Sort sort) {
		List<ProductGroup> result = integrationAIService.findAllProductGroups(sort);
		return ResponseEntity.ok(result.stream()
				.map(productGroup -> objectMapper.convertValue(productGroup, ProductGroupDTO.class)).toList());
	}

	/**
	 * @deprecated since PRJA-1628
	 */
	@Deprecated(since = "PRJA-1628")
	@GetMapping("product/platforms")
	public ResponseEntity<List<ProductPlatformDTO>> getAllProductPlatforms(Sort sort) {
		List<ProductPlatform> result = integrationAIService.findAllProductPlatform(sort);
		return ResponseEntity.ok(result.stream()
				.map(productPlatform -> objectMapper.convertValue(productPlatform, ProductPlatformDTO.class)).toList());
	}

	/**
	 * @deprecated since PRJA-1628
	 */
	@Deprecated(since = "PRJA-1628")
	@GetMapping("product/types/{platformCode}/{groupCode}")
	public ResponseEntity<List<ProductTypeDTO>> getAllProductTypesByPlatformCodeAndGroupCode(Sort sort,
			@PathVariable(name = "platformCode") String platformCode,
			@PathVariable(name = "groupCode") String groupCode) {
		validationService.validateGetAllProductTypes(platformCode, groupCode);
		List<ProductType> result = integrationAIService.findAllProductTypeByPlatformAndGroup(platformCode, groupCode,
				sort);
		return ResponseEntity.ok(result.stream()
				.map(productType -> objectMapper.convertValue(productType, ProductTypeDTO.class)).toList());
	}

	/**
	 * @deprecated since PRJA-1628
	 */
	@Deprecated(since = "PRJA-1628")
	@GetMapping("product/types/{platformCode}")
	public ResponseEntity<List<ProductTypeDTO>> getAllProductTypesByPlatFormCode(Sort sort,
			@PathVariable(name = "platformCode") String platformCode) {
		validationService.validateGetAllProductTypes(platformCode, null);
		List<ProductType> result = integrationAIService.findAllProductTypeByPlatformAndGroup(platformCode, null, sort);
		return ResponseEntity.ok(result.stream()
				.map(productType -> objectMapper.convertValue(productType, ProductTypeDTO.class)).toList());
	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@GetMapping("partner/{issuerType}/{issuerCode}")
	public ResponseEntity<PartnerResponse> getPartnerByIssuerCode(@PathVariable IssuerTypeEnum issuerType,
			@PathVariable String issuerCode) {
		PartnerResponse partnerResponse = null;
		switch (issuerType) {
			case BANK:
				BankListDTO bankList = bankFeignClient.getBankByIssuerCode(issuerCode);
				partnerResponse = PartnerResponse.builder().code(bankList.getCode()).name(bankList.getName())
						.active(bankList.getActive()).isPartner(bankList.getIsPartner())
						.issuerCode(bankList.getIssuerCode()).build();
				break;
			case INSURANCE:
				ProviderDTO provider = insuranceFeignClient.getProviderByIssuerCode(issuerCode);
				partnerResponse = PartnerResponse.builder().name(provider.getName()).active(provider.getActive())
						.isPartner(provider.getIsPartner()).issuerCode(provider.getIssuerCode()).build();
				break;
			default:
				throw new BusinessException(ErrorCodeEnum.ISSUER_TYPE_INVALID);
		}
		return ResponseEntity.ok(partnerResponse);
	}

	@GetMapping("partner/{issuerType}/{issuerCode}/{productId}")
	public ResponseEntity<PartnerResponse> getPartnerByIssuerCodeAndProductId(@PathVariable IssuerTypeEnum issuerType,
			@PathVariable String issuerCode, @PathVariable(required = false) String productId) {
		PartnerResponse partnerResponse = null;
		switch (issuerType) {
			case BANK:
				BankListDTO bankList = bankFeignClient.getBankByIssuerCodeAndProductId(issuerCode, productId);
				partnerResponse = PartnerResponse.builder().code(bankList.getCode()).name(bankList.getName())
						.active(bankList.getActive()).isPartner(bankList.getIsPartner())
						.issuerCode(bankList.getIssuerCode()).build();
				break;
			case INSURANCE:
				ProviderDTO provider = insuranceFeignClient.getProviderByIssuerCode(issuerCode);
				partnerResponse = PartnerResponse.builder().name(provider.getName()).active(provider.getActive())
						.isPartner(provider.getIsPartner()).issuerCode(provider.getIssuerCode()).build();

				if (Boolean.TRUE.equals(partnerResponse.isPartner())) {
					partnerResponse.setPartner(!bankFeignClient.checkProductInException(productId));
				}
				break;
			default:
				throw new BusinessException(ErrorCodeEnum.ISSUER_TYPE_INVALID);
		}
		return ResponseEntity.ok(partnerResponse);
	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@GetMapping("product-filters/loan-limit/{type}")
	public ResponseEntity<String> productFilterLoanLimit(@PathVariable String type) {
		return integrationAIService.findProductFilterLoanLimit(type);
	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@PostMapping("product-refinance/{type}")
	public ResponseEntity<String> productRefinance(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type,
			@Valid @RequestBody ProductLoanLimitRequest body) {
		return integrationAIService.findProductRefinance(type, body, page, size);
	}

	/**
	 * @deprecated since PRJA-1585
	 */
	@Deprecated(since = "PRJA-1585")
	@PostMapping("product-suggestions/loan-limit/{type}")
	public ResponseEntity<String> productSuggestionLoanLimit(@RequestParam(required = false) String page,
			@RequestParam(required = false) String size, @PathVariable String type,
			@Valid @RequestBody SuggestionLoanLimitRequest body) {
		return integrationAIService.findProductSuggestionLoanLimit(type, body, page, size);
	}

	/**
	 * @deprecated since PRJA-1500
	 */
	@Deprecated(since = "PRJA-1500")
	@GetMapping("get-questions")
	public ResponseEntity<String> getQuestion() {
		return integrationAIService.getQuestions();
	}

	/**
	 * @deprecated since PRJA-1500
	 */
	@Deprecated(since = "PRJA-1500")
	@PostMapping("answer")
	public ResponseEntity<String> postAnswer(@RequestBody AnswerRequest request) {
		return integrationAIService.processAnswer(request);
	}

	@PostMapping(value = "statement-job/upload", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
	public ResponseEntity<String> uploadStatementAsync(@RequestPart String issuerCode, @RequestPart String bankName,
			@RequestPart List<MultipartFile> files) throws JsonProcessingException {
		return integrationAIService.processStatementAsync(issuerCode, bankName, files);
	}

	// todo: once FE is fully integrated in PRJA-1189, then remove this api
	@GetMapping("statement-job/status/{id}")
	public ResponseEntity<String> getStatementJobByJobId(@PathVariable String id) {
		return integrationAIService.getStatementJobByJobId(id);
	}

}
