package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.TelcoOperator;
import my.com.mandrill.component.dto.model.TelcoOperatorDTO;
import my.com.mandrill.component.service.TelcoOperatorService;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("telco-operators")
public class TelcoOperatorController {

	private final TelcoOperatorService telcoOperatorService;

	@GetMapping
	public ResponseEntity<List<TelcoOperatorDTO>> findAll(@RequestParam(required = false) String name, Sort sort) {
		List<TelcoOperator> telcoOperators = telcoOperatorService.findAllActive(name, sort);
		return ResponseEntity.ok(telcoOperators.stream().map(MapStructConverter.MAPPER::toTelcoOperatorDTO).toList());
	}

	@GetMapping("{id}")
	public TelcoOperatorDTO findById(@PathVariable String id) {
		TelcoOperator telcoOperator = telcoOperatorService.findById(id);
		return MapStructConverter.MAPPER.toTelcoOperatorDTO(telcoOperator);
	}

	@Hidden
	@GetMapping("integration/by-product-code")
	public List<TelcoOperatorDTO> findByProductCode(@RequestParam List<String> productCodes) {
		List<TelcoOperator> telcoOperators = telcoOperatorService.findByProductCode(productCodes);
		return telcoOperators.stream().map(MapStructConverter.MAPPER::toTelcoOperatorDTO).toList();
	}

	@GetMapping("/product/{productCode}")
	public TelcoOperatorDTO findTelcoByProductCode(@PathVariable String productCode) {
		TelcoOperator telcoOperator = telcoOperatorService.findTelcoByProductCode(productCode);
		return MapStructConverter.MAPPER.toTelcoOperatorDTO(telcoOperator);
	}

}
