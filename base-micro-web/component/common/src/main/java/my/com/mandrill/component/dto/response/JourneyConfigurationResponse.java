package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.feign.dto.InterestTypeDTO;

import java.io.Serializable;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JourneyConfigurationResponse implements Serializable {

	private String name;

	private String description;

	private Set<InterestTypeDTO> interestTypes;

	private Integer sequence;

	private Boolean completeStatus = false;

	private String skipToStep;

	private Boolean skipOnComplete;

	private Boolean skipOnAddAccount;

	private Boolean doManualCompleteChecking;

}
