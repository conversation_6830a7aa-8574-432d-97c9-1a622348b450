package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.InterestType;
import my.com.mandrill.component.domain.JourneyConfiguration;
import my.com.mandrill.component.domain.JourneyFlow;
import my.com.mandrill.component.domain.UserJourney;

import java.util.Set;

public interface JourneyFlowService {

	JourneyFlow initialize(UserJourney userJourney, JourneyConfiguration journeyConfiguration,
			Set<InterestType> interestTypeSet);

	Boolean checkExistUpdateInterest(UserJourney userJourney, JourneyConfiguration journeyConfiguration,
			Set<InterestType> interestTypes);

}
