package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.UploadTransaction;
import my.com.mandrill.utilities.general.constant.UploadTransactionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface UploadTransactionService {

	Page<UploadTransaction> findAllUploadTransactions(Pageable page, UploadTransactionType type, String institutionId);

	UploadTransaction findByIdAndInstitutionId(String id, String institutionId);

	UploadTransaction create(UploadTransaction uploadTransaction);

	UploadTransaction update(UploadTransaction uploadTransaction);

	boolean existsInProgressUpload(UploadTransactionType type, String institutionId);

}
