package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.model.CountDTO;
import my.com.mandrill.component.service.StatisticsService;
import my.com.mandrill.utilities.feign.client.*;
import my.com.mandrill.utilities.general.constant.EntityName;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsServiceImpl implements StatisticsService {

	private final BankFeignClient bankFeignClient;

	private final InsuranceFeignClient insuranceFeignClient;

	private final PropertyFeignClient propertyFeignClient;

	private final UtilityFeignClient utilityFeignClient;

	private final VehicleFeignClient vehicleFeignClient;

	@Override
	public List<CountDTO> countModules(List<EntityName> entityNames) {
		return entityNames.stream().map(entityName -> {
			switch (entityName) {
				case BANK, CREDIT_CARD -> {
					return new CountDTO(entityName, bankFeignClient.countBank(entityName));
				}
				case PROPERTY -> {
					return new CountDTO(entityName, propertyFeignClient.count());
				}
				case VEHICLE -> {
					return new CountDTO(entityName, vehicleFeignClient.count());
				}
				case UTILITY -> {
					return new CountDTO(entityName, utilityFeignClient.count());
				}
				case INSURANCE -> {
					return new CountDTO(entityName, insuranceFeignClient.count());
				}
				case LOAN -> {
					return new CountDTO(entityName, bankFeignClient.countLoan());
				}
				default -> {
					return new CountDTO(entityName, null);
				}
			}
		}).toList();
	}

}
