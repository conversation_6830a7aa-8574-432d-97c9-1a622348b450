<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.19.xsd">

    <changeSet id="common-component_20230313_jose_0001" author="<PERSON>">
        <createTable tableName="journey_configuration">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_journey_configuration"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="active" type="BOOLEAN"/>
            <column name="sequence" type="INT"/>
            <column name="check_imbalance" type="BOOLEAN"/>
        </createTable>
        <addUniqueConstraint tableName="journey_configuration" columnNames="name" constraintName="jc_8319d401e5ca"/>
    </changeSet>
</databaseChangeLog>