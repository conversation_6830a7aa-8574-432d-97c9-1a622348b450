package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.FinanceType;
import my.com.mandrill.component.dto.model.FinanceTypeDTO;
import my.com.mandrill.component.service.impl.FinanceTypeServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@WebMvcTest(value = FinanceTypeController.class, excludeAutoConfiguration = SecurityAutoConfiguration.class)
@ContextConfiguration(classes = { FinanceTypeController.class })
class FinanceTypeControllerTest {

	public static final String PATH = "/finance-types";

	@Autowired
	MockMvc mockMvc;

	@Autowired
	ObjectMapper objectMapper;

	@MockBean
	FinanceTypeServiceImpl financeTypeService;

	List<FinanceType> mockResult;

	@BeforeEach
	void setup() {
		mockResult = new ArrayList<>();
		for (int i = 0; i < 5; i++) {
			String financeType = "%s%s".formatted("financeType", i);
			FinanceType entity = new FinanceType();
			entity.setId(UUID.randomUUID().toString());
			entity.setCode(financeType);
			entity.setName(financeType);
			entity.setDescription(financeType);
			entity.setActive(true);
			entity.setOthers(false);
			mockResult.add(entity);
		}
	}

	@Test
	void findByActiveTrue_ShouldReturn200AndActiveFinanceTypes_Positive() throws Exception {
		Mockito.when(financeTypeService.findByActiveTrue(any())).thenReturn(mockResult);
		List<FinanceTypeDTO> expectedResult = mockResult.stream()
				.map(financeType -> objectMapper.convertValue(financeType, FinanceTypeDTO.class)).toList();
		mockMvc.perform(MockMvcRequestBuilders.get(PATH).contentType(MediaType.APPLICATION_JSON)).andDo(print())
				.andExpect(status().isOk())
				.andExpect(MockMvcResultMatchers.content().json(objectMapper.writeValueAsString(expectedResult)))
				.andExpect(jsonPath("$[*].active").value(everyItem(is(true))));
	}

	@Test
	void findByIdAndActiveTrue_ShouldReturn200AndActiveFinanceType_Positive() throws Exception {
		FinanceType financeType = mockResult.get(0);
		FinanceTypeDTO expectedResult = objectMapper.convertValue(financeType, FinanceTypeDTO.class);
		Mockito.when(financeTypeService.findByIdAndActiveTrue(financeType.getId())).thenReturn(financeType);
		mockMvc.perform(MockMvcRequestBuilders.get(PATH + "/%s".formatted(financeType.getId()))
				.contentType(MediaType.APPLICATION_JSON)).andDo(print()).andExpect(status().isOk())
				.andExpect(MockMvcResultMatchers.content().json(objectMapper.writeValueAsString(expectedResult)))
				.andExpect(jsonPath("$.active").value(true));

	}

}
