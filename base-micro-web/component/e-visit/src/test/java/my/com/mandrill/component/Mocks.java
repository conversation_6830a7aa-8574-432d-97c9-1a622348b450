package my.com.mandrill.component;

import my.com.mandrill.component.constant.AccessStatusEnum;
import my.com.mandrill.component.constant.EAccessIntegrationErrorEnum;
import my.com.mandrill.component.constant.RegistrationFlowEnum;
import my.com.mandrill.component.domain.Access;
import my.com.mandrill.component.domain.AccessUnit;
import my.com.mandrill.component.domain.Favourite;
import my.com.mandrill.component.dto.model.AccessDTO;
import my.com.mandrill.component.dto.model.FavouriteRequestDTO;
import my.com.mandrill.component.dto.request.CreateAccessRequest;
import my.com.mandrill.component.dto.request.EAccessRegistrationRequest;
import my.com.mandrill.component.dto.request.VisitorDataUpdate;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.UserDTO;
import my.com.mandrill.utilities.general.dto.model.ObjectDTO;
import my.com.mandrill.utilities.general.util.DateConvertUtil;
import org.apache.commons.lang.RandomStringUtils;

import java.time.Instant;
import java.util.Collections;
import java.util.Random;

public class Mocks {

	private static final RegistrationFlowEnum[] flows = RegistrationFlowEnum.values();

	private static final AccessStatusEnum[] statuses = AccessStatusEnum.values();

	private static final Random RANDOM = new Random();

	public static String BEARER_TOKEN = "Bearer Token";

	public static String generateRandomString() {
		int length = 10;
		boolean useLetters = true;
		boolean useNumbers = false;

		return RandomStringUtils.random(length, useLetters, useNumbers);
	}

	public static Boolean generateRandomBoolean() {
		return Math.random() < 0.5;
	}

	public static Long generateRandomLong() {
		return RANDOM.nextLong();
	}

	public static EAccessBuildingDataResponse mockEAccessBuildingDataResponse() {
		EAccessBuildingDataResponse eAccessBuildingDataResponse = new EAccessBuildingDataResponse();
		eAccessBuildingDataResponse.setName(generateRandomString());
		eAccessBuildingDataResponse.setCode(generateRandomString());
		eAccessBuildingDataResponse.setActive(generateRandomBoolean());

		return eAccessBuildingDataResponse;
	}

	public static EAccessTenantDataResponse mockEAccessTenantDataResponse() {
		EAccessTenantDataResponse eAccessTenantDataResponse = new EAccessTenantDataResponse();
		eAccessTenantDataResponse.setName(generateRandomString());
		eAccessTenantDataResponse.setCode(generateRandomString());
		eAccessTenantDataResponse.setActive(generateRandomBoolean());
		eAccessTenantDataResponse.setUnits(Collections.singletonList(mockEAccessUnitDataResponse()));

		return eAccessTenantDataResponse;
	}

	public static EAccessUnitDataResponse mockEAccessUnitDataResponse() {
		EAccessUnitDataResponse eAccessUnitDataResponse = new EAccessUnitDataResponse();
		eAccessUnitDataResponse.setName(generateRandomString());
		eAccessUnitDataResponse.setActive(generateRandomBoolean());

		return eAccessUnitDataResponse;
	}

	private static EAccessRequesterDataResponse mockEAccessRequesterDataResponse() {
		EAccessRequesterDataResponse eAccessRequesterDataResponse = new EAccessRequesterDataResponse();
		eAccessRequesterDataResponse.setName(generateRandomString());

		return eAccessRequesterDataResponse;
	}

	public static EAccessInvitationDataResponse mockEAccessInvitationDataResponse() {
		EAccessInvitationDataResponse eAccessInvitationDataResponse = new EAccessInvitationDataResponse();
		eAccessInvitationDataResponse.setUuid(generateRandomString());
		eAccessInvitationDataResponse.setStart(Instant.now());
		eAccessInvitationDataResponse.setEnd(Instant.now());
		eAccessInvitationDataResponse.setBuilding(mockEAccessBuildingDataResponse());
		eAccessInvitationDataResponse.setRequester(mockEAccessRequesterDataResponse());
		eAccessInvitationDataResponse.setUnits(Collections.singletonList(mockEAccessUnitDataResponse()));
		eAccessInvitationDataResponse.setTenant(mockEAccessTenantDataResponse());

		return eAccessInvitationDataResponse;
	}

	public static EAccessRegistrationRequest mockEAccessRegistrationRequest() {
		EAccessRegistrationRequest request = new EAccessRegistrationRequest();
		request.setName(generateRandomString());
		request.setIdType(generateRandomString());
		request.setNationality(generateRandomString());
		request.setIdNo(generateRandomString());
		request.setPicture(generateRandomString());
		request.setEmail(generateRandomString());
		request.setContactNo(generateRandomString());
		request.setUuid(generateRandomString());
		request.setBuildingName(generateRandomString());
		request.setBuildingCode(generateRandomString());
		request.setTenantName(generateRandomString());
		request.setTenantCode(generateRandomString());
		request.setUnits(Collections.singletonList(generateRandomString()));
		request.setVisitStart(Instant.now().toString());
		request.setVisitEnd(Instant.now().toString());

		return request;
	}

	public static EAccessRegistrationResponse mockSuccessfulEAccessRegistrationResponse() {
		EAccessRegistrationResponse response = new EAccessRegistrationResponse();
		response.setSuccess(1);
		response.setVisitorId(generateRandomLong());

		return response;
	}

	public static EAccessRegistrationResponse mockDuplicatedEAccessRegistrationResponse() {
		EAccessRegistrationResponse response = new EAccessRegistrationResponse();
		response.setErrorCode(EAccessIntegrationErrorEnum.DUPLICATE_REGISTRATION.getCode());

		return response;
	}

	public static VisitorDataUpdate mockVisitorDataUpdate() {
		VisitorDataUpdate visitorDataUpdate = new VisitorDataUpdate();
		visitorDataUpdate.setVisitorId(generateRandomString());
		visitorDataUpdate.setFullName(generateRandomString());
		visitorDataUpdate.setStaffId(generateRandomString());
		visitorDataUpdate.setIdType(generateRandomString());
		visitorDataUpdate.setIdNo(generateRandomString());
		visitorDataUpdate.setEmail(generateRandomString());
		visitorDataUpdate.setContactNo(generateRandomString());
		visitorDataUpdate.setCompanyName(generateRandomString());
		visitorDataUpdate.setDesignation(generateRandomString());
		visitorDataUpdate.setGender(generateRandomString());
		visitorDataUpdate.setNationality(generateRandomString());
		visitorDataUpdate.setDateStart(DateConvertUtil.toString(Instant.now(), DateConvertUtil.DATETIME_FORMAT_6));
		visitorDataUpdate.setDateEnd(DateConvertUtil.toString(Instant.now(), DateConvertUtil.DATETIME_FORMAT_6));
		visitorDataUpdate.setEntryFrequency(generateRandomString());
		visitorDataUpdate.setStaffName(generateRandomString());
		visitorDataUpdate.setSpaceId(generateRandomString());
		visitorDataUpdate.setVisitorType(generateRandomString());
		visitorDataUpdate.setUuid(generateRandomString());
		visitorDataUpdate.setSiteName(generateRandomString());
		visitorDataUpdate.setSiteCode(generateRandomString());
		visitorDataUpdate.setTenantName(generateRandomString());
		visitorDataUpdate.setTenantCode(generateRandomString());
		visitorDataUpdate.setUnits(Collections.singletonList(generateRandomString()));
		visitorDataUpdate.setFrPicture(generateRandomString());
		visitorDataUpdate.setHashCode(generateRandomString());

		return visitorDataUpdate;
	}

	public static Access mockAccess() {
		Access access = new Access();
		access.setId(generateRandomString());
		access.setCreatedDate(Instant.now());
		access.setCreatedBy(generateRandomString());
		access.setLastModifiedDate(Instant.now());
		access.setLastModifiedBy(generateRandomString());
		access.setUserId(generateRandomString());
		access.setBuildingName(generateRandomString());
		access.setBuildingCode(generateRandomString());
		access.setTenantName(generateRandomString());
		access.setTenantCode(generateRandomString());
		access.setRegistrationDatetime(Instant.now());
		access.setConfirmationDatetime(Instant.now());
		access.setVisitDatetimeStart(Instant.now());
		access.setVisitDatetimeEnd(Instant.now());
		access.setRegistrationFlow(flows[RANDOM.nextInt(flows.length)]);
		access.setExternalId(generateRandomString());
		access.setVisitorId(generateRandomString());
		access.setStatus(statuses[RANDOM.nextInt(statuses.length)]);
		access.setErrorMessage(generateRandomString());
		access.setIsReminder(generateRandomBoolean());
		access.setUnits(Collections.singleton(mockAccessUnit(access)));

		return access;
	}

	public static AccessUnit mockAccessUnit(Access access) {
		AccessUnit accessUnit = new AccessUnit();
		accessUnit.setUnit(generateRandomString());
		accessUnit.setAccess(access != null ? access : mockAccess());
		accessUnit.setId(generateRandomString());

		return accessUnit;
	}

	public static CreateAccessRequest mockCreateAccessRequest() {
		CreateAccessRequest createAccessRequest = new CreateAccessRequest();
		createAccessRequest.setBuildingName(generateRandomString());
		createAccessRequest.setBuildingCode(generateRandomString());
		createAccessRequest.setTenantName(generateRandomString());
		createAccessRequest.setTenantCode(generateRandomString());
		createAccessRequest.setUnits(Collections.singletonList(generateRandomString()));
		createAccessRequest.setVisitDatetimeStart(Instant.now());
		createAccessRequest.setVisitDatetimeEnd(Instant.now());
		createAccessRequest.setReminder(generateRandomBoolean());

		return createAccessRequest;
	}

	public static AccessDTO mockAccessDTO() {
		AccessDTO accessDTO = new AccessDTO();
		accessDTO.setId(generateRandomString());
		accessDTO.setUserId(generateRandomString());
		accessDTO.setBuildingName(generateRandomString());
		accessDTO.setBuildingCode(generateRandomString());
		accessDTO.setTenantName(generateRandomString());
		accessDTO.setTenantCode(generateRandomString());
		accessDTO.setUnits(Collections.singletonList(generateRandomString()));
		accessDTO.setVisitDatetimeStart(Instant.now());
		accessDTO.setVisitDatetimeEnd(Instant.now());
		accessDTO.setReminder(generateRandomBoolean());
		accessDTO.setStatus(statuses[RANDOM.nextInt(statuses.length)]);
		accessDTO.setRegistrationFlow(flows[RANDOM.nextInt(flows.length)]);
		accessDTO.setExternalId(generateRandomString());

		return accessDTO;
	}

	public static UserDTO mockUserDTO() {
		UserDTO userDTO = new UserDTO();
		userDTO.setId(generateRandomString());

		ObjectDTO nationality = new ObjectDTO();
		nationality.setId(generateRandomString());

		userDTO.setNationality(nationality);

		return userDTO;
	}

	public static CurrentUserIdDTO mockCurrentUserIdDTO() {
		CurrentUserIdDTO currentUserIdDTO = new CurrentUserIdDTO();
		currentUserIdDTO.setId(generateRandomString());
		currentUserIdDTO.setRefNo(generateRandomString());

		return currentUserIdDTO;
	}

	public static FavouriteRequestDTO mockFavouriteRequestDTO() {
		FavouriteRequestDTO requestDTO = new FavouriteRequestDTO();
		requestDTO.setBuildingCode(generateRandomString());
		requestDTO.setBuildingName(generateRandomString());
		requestDTO.setTenantCode(generateRandomString());
		requestDTO.setTenantName(generateRandomString());
		requestDTO.setExternalId(generateRandomString());
		requestDTO.setUnits(Collections.singletonList(generateRandomString()));

		return requestDTO;
	}

	public static Favourite mockFavourite() {
		Favourite favourite = new Favourite();
		favourite.setBuildingCode(generateRandomString());
		favourite.setBuildingName(generateRandomString());
		favourite.setTenantCode(generateRandomString());
		favourite.setTenantName(generateRandomString());
		favourite.setExternalId(generateRandomString());
		favourite.setUnit(generateRandomString());

		return favourite;
	}

	public static EAccessAuthenticationResponse mockEAccessAuthenticationResponse(Instant expiryDate) {
		EAccessAuthenticationResponse response = new EAccessAuthenticationResponse();
		response.setIdToken(generateRandomString());
		response.setRefreshToken(generateRandomString());
		response.setExpiryDate(expiryDate != null ? expiryDate : Instant.now().plusSeconds(60 * 60 * 24));

		return response;
	}

}
