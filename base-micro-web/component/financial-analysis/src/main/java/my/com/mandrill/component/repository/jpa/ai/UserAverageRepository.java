package my.com.mandrill.component.repository.jpa.ai;

import my.com.mandrill.component.domain.ai.fmf.UserAverage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.time.Instant;

@Repository
public interface UserAverageRepository extends JpaRepository<UserAverage, String> {

	UserAverage findFirstByUserIdOrderByCreatedAtDesc(@NonNull String userId);

	@Query("""
				select count(distinct u.userId) as user_count
				from UserAverage u
			where u.createdAt between :createdDateStart and :createdDateEnd
			and u.userId not in (
				select u.userId
				from UserAverage u
				where createdAt < :createdDateStart
			)
			""")
	long countNewUserAverageBetweenDate(Instant createdDateStart, Instant createdDateEnd);

}
