package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import my.com.mandrill.utilities.core.audit.AuditSection;

@Getter
@Setter
@Entity
@Table(name = "cover_type")
public class CoverType extends AuditSection {

	@NotBlank
	@Size(max = 255)
	@Column(name = "code", nullable = false)
	private String code;

	@NotBlank
	@Size(max = 255)
	@Column(name = "description")
	private String description;

	@NotBlank
	@Size(max = 36)
	@Column(name = "provider_id")
	private String providerId;

	@ToString.Exclude
	@ManyToOne(optional = false)
	@JoinColumn(name = "vehicle_type_id", nullable = false)
	private VehicleType vehicleType;

}
