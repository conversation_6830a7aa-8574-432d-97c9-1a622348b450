package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubmitPriceQuotationRequest implements Serializable {

	@NotBlank
	private String genderCode;

	@NotBlank
	private String maritalStatusCode;

	private String vehicleId;

	private String vixTransactionCode;

	private BigDecimal vehicleSumInsured;

	@NotBlank
	private String coverTypeCode;

	@NotBlank
	private String email;

	@NotBlank
	private String mobileNumber;

	@Size(min = 2, max = 3)
	private List<String> address;

	@NotBlank
	private String dob;

	private RoadTaxInfo roadTaxInfo;

	@Builder.Default
	@NotNull
	private String quotationNo = "";

	private List<AdditionalDriver> additionalDrivers;

	@NotBlank
	private String postcode;

	private ExtraFields extraFields;

	@Builder.Default
	private String insurerCode = "";

	@Builder.Default
	private String currentInsurerCode = "";

	@Data
	public static class RoadTaxInfo implements Serializable {

		private String roadTaxRequired;

		private List<String> roadTaxDeliveryAddress;

		private String roadTaxDeliveryCity;

		private String roadTaxDeliveryState;

		private String roadTaxDeliveryPostcode;

	}

	@Data
	public static class AdditionalDriver implements Serializable {

		private String name;

		private String idType;

		private String identityNo;

		private String dob;

		private String gender;

		private String maritalStatus;

		private String relationship;

		private String occupation;

	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ExtraFields implements Serializable {

		private List<ExtraCoverageCode> extraCoverageCode;

	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ExtraCoverageCode implements Serializable {

		private String code;

		private BigDecimal coverageRequestedValue;

	}

}
