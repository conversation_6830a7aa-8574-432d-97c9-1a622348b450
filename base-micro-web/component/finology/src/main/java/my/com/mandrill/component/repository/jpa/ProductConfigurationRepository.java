package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Gender;
import my.com.mandrill.component.domain.ProductConfiguration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ProductConfigurationRepository extends JpaRepository<ProductConfiguration, String> {

	ProductConfiguration findByInsurerCodeAndUsageTypeAndVehicleTypeAndCoverTypeCodeAndLang(String insurerCode,
			String usageType, String vehicleType, String coverTypeCode, String lang);

}
