<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="finology-component_20231101_maulana_0001" author="maulana">
        <createTable tableName="vix_request">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_vix_request"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false" />
            </column>
            <column name="ref_code" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
            <column name="request_date_time" type="DATETIME">
                <constraints nullable="false" />
            </column>
            <column name="hash_code" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
            <column name="vehicle_registration_no" type="VARCHAR(32)">
                <constraints nullable="false" />
            </column>
            <column name="id_type" type="VARCHAR(36)">
                <constraints nullable="false" />
            </column>
            <column name="identification_no" type="VARCHAR(16)">
                <constraints nullable="false" />
            </column>
            <column name="postcode" type="VARCHAR(5)"/>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>