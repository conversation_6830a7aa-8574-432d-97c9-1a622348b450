package my.com.mandrill.component.controller;

import my.com.mandrill.component.domain.VehicleType;
import my.com.mandrill.component.service.VehicleTypeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(value = VehicleTypeController.class, excludeAutoConfiguration = SecurityAutoConfiguration.class)
@AutoConfigureMockMvc
@ContextConfiguration(classes = VehicleTypeController.class)
class VehicleTypeControllerTest {

	@Autowired
	MockMvc mockMvc;

	@MockBean
	VehicleTypeService vehicleTypeService;

	String baseUrl = "/vehicle-types";

	VehicleType vehicleTypeMock;

	List<VehicleType> vehicleTypeListMock;

	@BeforeEach
	void setup() {
		vehicleTypeMock = new VehicleType();
		vehicleTypeListMock = new ArrayList<>();
	}

	@Test
	void findAll_GiveValidArgument_Positive() throws Exception {
		vehicleTypeListMock.add(vehicleTypeMock);
		when(vehicleTypeService.findAllVehicleType()).thenReturn(vehicleTypeListMock);

		mockMvc.perform(MockMvcRequestBuilders.get(baseUrl)).andDo(print()).andExpect(status().isOk())
				.andExpect(jsonPath("$", hasSize(1)));
	}

}
