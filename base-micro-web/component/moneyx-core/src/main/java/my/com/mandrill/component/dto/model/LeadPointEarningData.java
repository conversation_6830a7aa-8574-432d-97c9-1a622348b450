package my.com.mandrill.component.dto.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.utilities.general.constant.PointEarningStatus;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeadPointEarningData {

	private String id;

	private String refNo;

	private String userRefNo;

	private String fullName;

	private String companyId;

	private String companyName;

	private BigDecimal pointAmount;

	private BigDecimal pointPercentage;

	private RSMFocalType rsmDetailFocalType;

	private String transactionId;

	private PointEarningStatus status;

	private String createdBy;

	private Instant createdDate;

	private String disbursedBy;

	private Instant disbursedDate;

}
