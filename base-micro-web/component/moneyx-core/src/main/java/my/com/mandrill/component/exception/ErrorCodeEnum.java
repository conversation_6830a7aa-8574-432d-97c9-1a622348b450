package my.com.mandrill.component.exception;

import lombok.Getter;
import my.com.mandrill.utilities.general.exception.ExceptionEnum;

@Getter
public enum ErrorCodeEnum implements ExceptionEnum {

	REFERRAL_CODE_EXISTS("CORE0001", "Referral code already exist"),
	REFERRAL_CODE_NOT_EXISTS("CORE0002", "Referral code not exist"),
	FAILED_TO_PROCESS_WITHDRAWAL("CORE0003", "Failed to process withdrawal"),
	REVENUE_DISTRIBUTION_NOT_TALLY_WITH_REVENUE("CORE0004",
			"The product revenue and the sum of revenue distribution do not tally. Please ensure they are the same."),
	REVENUE_DISTRIBUTION_PERCENTAGE_NOT_TALLY("CORE0005",
			"Please ensure that all RSM Focal Types achieve a revenue distribution percentile (%) that evenly amounts up to 100%."),
	INVALID_REVENUE("CORE0006", "Revenue must be between %s and %s"),
	INVALID_REVENUE_DISTRIBUTION("CORE0007", "Revenue Distribution must not be negative."),
	INVALID_PRODUCT_GROUP_OR_TYPE("CORE0008", "Product group or product type not exist."),
	DUPLICATE_ATTACHED_RSM("CORE0009", "Duplicated attached RSM"),
	REFERRER_RELATION_NOT_EXISTS("CORE0010", "Referrer relation not exists"),
	RSM_COMMISSION_CONFIRMED("CORE0011",
			"Deletion cannot be performed on this record because the RSM Commission has been confirmed"),
	RSM_COMMISSION_EXISTS("CORE0012", "An RSM commission for this product and reward name already exists"),
	RSM_HEADER_EXISTS("CORE0013", "A RSM Commission with the same RSM details exists. Please modify accordingly."),
	RSM_MODIFY_STATUS_NOT_ALLOWED("CORE0014",
			"It is not allowed to change the status of an application that has been attached to a commission"),
	REQUIRED_FIELD_EMPTY("CORE0015",
			"Please ensure that all compulsory fields and all RSM Revenue Distribution fields are filled."),
	RSM_COMMISSION_REFEREE_B2B_AND_NON_REFERRAL_DISTRIBUTION_NOT_EQUAL("CORE0016",
			"Revenue distribution for Referee/User level for B2B and non-referral are not equal."),
	RSM_COMMISSION_REFEREE_REVENUE_DISTRIBUTION_SHARING_DIFFERENT("CORE0017",
			"Please ensure that all Referees/Users are achieving the same RSM revenue distribution sharing."),
	RSM_COMMISSION_STATUS_IS_NOT_SCHEDULED_OR_DRAFT("CORE0018", "RSM status is not scheduled or draft.");

	private final String code;

	private final String description;

	ErrorCodeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

}