<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="moneyx-core-component_20250526_monika_0001" author="monika">
        <addColumn tableName="rsm_header">
            <column name="frequency" type="VARCHAR(100)"/>
            <column name="status" type="VARCHAR(100)"/>
            <column name="start_date" type="DATETIME"/>
            <column name="end_date" type="DATETIME"/>
            <column name="external_revenue" type="DECIMAL(15,2)"/>
            <column name="external_reward_name" type="VARCHAR(255)"/>
            <column name="external_start_date" type="DATETIME"/>
            <column name="external_end_date" type="DATETIME"/>
        </addColumn>

        <addColumn tableName="rsm_detail">
            <column name="external_b2b_c2c_non_referral_revenue_distribution" type="VARCHAR(255)"/>
            <column name="external_b2b2c_revenue_distribution" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>