package my.com.mandrill.component.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NotificationSegment {

	// default one signal segment
	SUBSCRIBED_USERS("Subscribed Users"), ACTIVE_USERS("Active Users"), TOTAL_SUBSCRIPTIONS("Total Subscriptions"),
	PLATFORM_ANDROID("Type: ANDROID"), PLATFORM_IOS("Type: IOS"), PLATFORM_HUAWEI("Type: HUAWEI"),;

	private final String code;

}
