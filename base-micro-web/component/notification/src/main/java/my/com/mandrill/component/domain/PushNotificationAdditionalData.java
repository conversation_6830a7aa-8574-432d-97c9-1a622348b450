package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;
import my.com.mandrill.component.constant.PushNotificationAdditionalDataSource;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;

@Entity
@Table(name = "push_notification_additional_data",
		uniqueConstraints = { @UniqueConstraint(columnNames = { "push_notification_id", "key" }) })
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PushNotificationAdditionalData extends AuditSection implements Serializable {

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonBackReference
	@JoinColumn(name = "push_notification_id", nullable = false)
	private PushNotification pushNotification;

	@Column(name = "key", length = 36, nullable = false)
	private String key;

	@Column(name = "value", nullable = false)
	private String value;

	@Column(name = "source", length = 50, nullable = false)
	@Enumerated(EnumType.STRING)
	private PushNotificationAdditionalDataSource source = PushNotificationAdditionalDataSource.USER_INPUT;

}
