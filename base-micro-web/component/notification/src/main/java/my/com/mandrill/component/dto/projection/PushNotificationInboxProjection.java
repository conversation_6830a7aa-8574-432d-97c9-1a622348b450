package my.com.mandrill.component.dto.projection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.EventType;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PushNotificationInboxProjection implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	private String id;

	private EventType eventType;

	private String imageUrl;

	private String launchUrl;

	private String message;

}
