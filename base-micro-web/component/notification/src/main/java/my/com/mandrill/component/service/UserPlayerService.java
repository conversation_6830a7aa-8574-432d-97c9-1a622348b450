package my.com.mandrill.component.service;

import com.onesignal.client.ApiException;
import my.com.mandrill.component.domain.UserPlayer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface UserPlayerService {

	UserPlayer save(UserPlayer userPlayer) throws ApiException;

	void delete(String userId, String playerId) throws ApiException;

	void delete(String playerId);

	Page<UserPlayer> findAllByUserId(String userId, Pageable pageable);

	UserPlayer findByUserIdAndPlayerId(String userId, String playerId);

	List<UserPlayer> findAllByUserId(String userId);

	List<String> findAllPlayerIdByUserId(String userId);

	UserPlayer findByPlayerId(String playerId);

	Page<UserPlayer> findAll(Pageable pageable);

	List<String> findAllPlayerId();

	boolean existsByUserId(String userId);

}
