package my.com.mandrill.component.service.impl;

import com.infobip.ApiClient;
import com.infobip.RequestDefinition;
import com.infobip.api.SmsApi;
import com.infobip.api.WhatsAppApi;
import com.infobip.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.InfoBipProperties;
import my.com.mandrill.component.dto.model.infobip.InfoBIPPushNotificationDTO;
import my.com.mandrill.component.service.InfoBipService;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class InfoBipServiceImpl implements InfoBipService {

	private final InfoBipProperties infoBipProperties;

	private final ApiClient infoBipApiClient;

	@Override
	public SmsResponse sendSMSMessage(String recipient, String smsText) throws Exception {
		SmsApi smsApi = new SmsApi(infoBipApiClient);
		SmsTextualMessage smsMessage = new SmsTextualMessage().from(infoBipProperties.getFrom())
				.addDestinationsItem(new SmsDestination().to(recipient)).text(smsText);
		SmsAdvancedTextualRequest smsMessageRequest = new SmsAdvancedTextualRequest().messages(List.of(smsMessage));
		log.info(String.valueOf(smsMessageRequest));
		return smsApi.sendSmsMessage(smsMessageRequest).execute();
	}

	@Override
	public WhatsAppBulkMessageInfo sendWhatsappOTPTemplateMessage(String recipient, String otp) {
		WhatsAppApi whatsAppApi = new WhatsAppApi(infoBipApiClient);
		WhatsAppTemplateBodyContent whatsAppTemplateBodyContent = new WhatsAppTemplateBodyContent()
				.placeholders(List.of(otp));
		WhatsAppTemplateUrlButtonContent whatsAppTemplateUrlButtonContent = new WhatsAppTemplateUrlButtonContent()
				.parameter(otp);
		WhatsAppTemplateDataContent whatsAppTemplateDataContent = new WhatsAppTemplateDataContent()
				.body(whatsAppTemplateBodyContent).buttons(List.of(whatsAppTemplateUrlButtonContent));
		WhatsAppTemplateContent whatsAppTemplateContent = new WhatsAppTemplateContent().templateName("authentication")
				.templateData(whatsAppTemplateDataContent).language("en_GB");
		WhatsAppMessage whatsAppMessage = new WhatsAppMessage().from(infoBipProperties.getWhatsAppFrom()).to(recipient)
				.content(whatsAppTemplateContent);
		WhatsAppBulkMessage whatsAppBulkMessage = new WhatsAppBulkMessage();
		whatsAppBulkMessage.addMessagesItem(whatsAppMessage);
		log.info(String.valueOf(whatsAppBulkMessage));
		try {
			return whatsAppApi.sendWhatsAppTemplateMessage(whatsAppBulkMessage).execute();
		}
		catch (Exception e) {
			log.error("Failed to send WhatsApp OTP message: {}", e.getMessage());
			return null;
		}
	}

	@Override
	public void sendPushNotification(InfoBIPPushNotificationDTO payload) {
		try {
			RequestDefinition definition = RequestDefinition.builder("POST", "/push/3/messages").body(payload)
					.requiresAuthentication(true).accept("application/json").contentType("application/json").build();

			String response = infoBipApiClient.execute(definition, String.class);
			log.debug("InfoBip push notification response: {}", response);
		}
		catch (Exception e) {
			log.error("Error when send notification info-bip: {}", e.getMessage());
		}
	}

}