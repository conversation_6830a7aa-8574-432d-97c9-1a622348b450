package my.com.mandrill.component.service.impl;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.MessageTemplate;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.MessageTemplateRepository;
import my.com.mandrill.component.service.MessageTemplateService;
import my.com.mandrill.utilities.general.constant.ChannelCodeEnum;
import my.com.mandrill.utilities.general.constant.MessageTemplateCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional(readOnly = true)
public class MessageTemplateServiceImpl implements MessageTemplateService {

	@Autowired
	private MessageTemplateRepository messageTemplateRepository;

	@Override
	public List<MessageTemplate> findByActiveCode(MessageTemplateCodeEnum code) {
		return messageTemplateRepository.findByCodeAndActiveTrue(code);
	}

	@Override
	public MessageTemplate findByActiveCodeAndChannel(MessageTemplateCodeEnum code, ChannelCodeEnum channel) {
		return messageTemplateRepository.findByCodeAndChannelAndActiveTrue(code, channel).orElseThrow(
				ExceptionPredicate.messageTemplateByCodeAndChannelNotFound(code.getValue(), channel.getValue()));
	}

}
