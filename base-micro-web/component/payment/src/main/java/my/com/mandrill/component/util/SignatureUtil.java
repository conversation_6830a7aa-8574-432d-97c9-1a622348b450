package my.com.mandrill.component.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.domain.Transaction;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.apache.commons.codec.digest.DigestUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SignatureUtil {

	public static String hashRequestSignature(Transaction transaction) {
		try {
			return DigestUtils.sha512Hex(String
					.format("%s;%s;%s;%s;%s", transaction.getMerchant().getSignatureKey(),
							transaction.getMerchant().getCode(), transaction.getRefNo(),
							transaction.getAmount().movePointRight(2).intValueExact(), transaction.getCurrency())
					.toUpperCase());
		}
		catch (ArithmeticException e) {
			throw new BusinessException(ErrorCodeEnum.WRONG_AMOUNT_FORMAT);
		}

	}

	public static String hashResponseSignature(Transaction transaction) {
		try {
			return DigestUtils
					.sha512Hex(String.format("%s;%s;%s;%s;%s;%s;%s", transaction.getMerchant().getSignatureKey(),
							transaction.getMerchant().getCode(), transaction.getPaymentId(), transaction.getRefNo(),
							transaction.getAmount().movePointRight(2).intValueExact(), transaction.getCurrency(),
							transaction.getPaymentStatus()).toUpperCase());
		}
		catch (ArithmeticException e) {
			throw new BusinessException(ErrorCodeEnum.WRONG_AMOUNT_FORMAT);
		}

	}

}
