package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.WebCampaign;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.Set;

public interface WebCampaignService {

	WebCampaign save(WebCampaign webCampaign);

	WebCampaign findById(String id);

	Page<WebCampaign> findAll(Pageable pageable, String name);

	void delete(WebCampaign webCampaign);

	Optional<WebCampaign> findBySequence(int sequence);

	Set<WebCampaign> findAllWebCampaignsPublic(String category, String productId, Boolean isHighlight,
			String subCategory);

	WebCampaign findBySlug(String slug);

}
