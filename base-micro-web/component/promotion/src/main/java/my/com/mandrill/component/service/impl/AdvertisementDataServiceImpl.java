package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Advertisement;
import my.com.mandrill.component.domain.AdvertisementAdditionalData;
import my.com.mandrill.component.repository.jpa.AdvertisementAdditionalDataRepository;
import my.com.mandrill.component.service.AdvertisementDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AdvertisementDataServiceImpl implements AdvertisementDataService {

	private final AdvertisementAdditionalDataRepository advertisementAdditionalDataRepository;

	@Override
	public List<AdvertisementAdditionalData> findByAdvertisement(Advertisement advertisement) {
		return advertisementAdditionalDataRepository.findByAdvertisement(advertisement);
	}

}
