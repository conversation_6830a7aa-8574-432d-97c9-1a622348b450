package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.domain.ReferralReferrer;
import my.com.mandrill.component.dto.request.ReferralReferrerRequest;
import my.com.mandrill.component.service.ReferralReferrerIntegrationService;
import my.com.mandrill.component.service.ReferralReferrerService;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;

@Service
@RequiredArgsConstructor
public class ReferralReferrerIntegrationServiceImpl implements ReferralReferrerIntegrationService {

	private final ReferralReferrerService referralReferrerService;

	@Override
	public ReferralReferrer createReferralReferrer(ReferralReferrerRequest referralReferrerRequest, String userId) {

		ReferralReferrer referralReferrer = new ReferralReferrer();
		referralReferrer.setUserId(userId);
		referralReferrer.setReferralCode(referralReferrerRequest.getReferralCode());
		referralReferrer.setReferrerVendorId(referralReferrerRequest.getReferrerVendorId());
		referralReferrer.setTotalConversions(0);

		return referralReferrerService.save(referralReferrer);
	}

	@Override
	public ReferralReferrer updateReferralReferrerTotalConversions(String referralReferrerId) {
		ReferralReferrer referralReferrer = referralReferrerService.findById(referralReferrerId);
		referralReferrer.setTotalConversions(referralReferrer.getTotalConversions() + 1);
		return referralReferrerService.save(referralReferrer);
	}

	@Override
	public long countTotalConversionsBetweenDate(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		Instant dateStart = dateFrom.atStartOfDay().atZone(zoneId).toInstant();
		Instant dateEnd = dateTo.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		return referralReferrerService.countTotalConversionsBetweenDate(dateStart, dateEnd);
	}

}
