package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.PropertyFacilitiesParameter;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Set;

public interface PropertyFacilityParameterRepository extends JpaRepository<PropertyFacilitiesParameter, String> {

	Set<PropertyFacilitiesParameter> findByProjectIdAndPropertyParameterIdIn(String projectId,
			List<String> parameterIds);

	int deleteByProjectIdAndIdNotIn(String projectId, List<String> ids);

	@EntityGraph(attributePaths = { "propertyParameter" })
	List<PropertyFacilitiesParameter> findByProjectIdIn(List<String> projectId);

}
