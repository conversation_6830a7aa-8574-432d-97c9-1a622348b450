package my.com.mandrill.component.repository.jpa.spec;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import my.com.mandrill.component.domain.Project;
import my.com.mandrill.component.domain.ProjectPropertyType;
import my.com.mandrill.component.domain.ProjectUnit;
import my.com.mandrill.component.domain.PropertySubType;
import my.com.mandrill.component.dto.request.PropertySearchRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;

public class ProjectSearchSpecification {

	public static Specification<Project> withFilters(PropertySearchRequest propertySearchRequest) {

		return (root, query, criteriaBuilder) -> {
			List<Predicate> predicates = new ArrayList<>();
			predicates.add(criteriaBuilder.isTrue(root.get(Project.Fields.active)));
			predicates.add(criteriaBuilder.isTrue(root.get(Project.Fields.developer).get("active")));

			Join<Project, ProjectUnit> projectUnitJoin = root.join(Project.Fields.projectUnits, JoinType.INNER);
			predicates.add(criteriaBuilder.isTrue(projectUnitJoin.get(ProjectUnit.Fields.active)));

			if (propertySearchRequest.getMinPrice() != null && propertySearchRequest.getMaxPrice() != null) {
				predicates.add(criteriaBuilder.between(projectUnitJoin.get("price"),
						propertySearchRequest.getMinPrice(), propertySearchRequest.getMaxPrice()));
			}

			if (propertySearchRequest.getMinSize() != null && propertySearchRequest.getMaxSize() != null) {
				Join<Project, ProjectUnit> projectProjectUnitJoin = root.join(Project.Fields.projectUnits,
						JoinType.INNER);
				predicates.add(criteriaBuilder.between(projectProjectUnitJoin.get("size"),
						propertySearchRequest.getMinSize(), propertySearchRequest.getMaxSize()));
			}

			if (CollectionUtils.isNotEmpty(propertySearchRequest.getPropertyTypeIds())) {
				Join<Project, ProjectPropertyType> projectProjectPropertyTypeJoin = root
						.join(Project.Fields.propertyTypes, JoinType.INNER);
				Join<ProjectPropertyType, PropertySubType> projectPropertyTypePropertySubTypeJoin = projectProjectPropertyTypeJoin
						.join("type", JoinType.INNER);
				CriteriaBuilder.In<String> inClause = criteriaBuilder
						.in(projectPropertyTypePropertySubTypeJoin.get("id"));
				for (String propertyTypeId : propertySearchRequest.getPropertyTypeIds()) {
					inClause.value(propertyTypeId);
				}
				predicates.add(inClause);
			}

			if (CollectionUtils.isNotEmpty(propertySearchRequest.getCategoryIds())) {
				predicates.add(root.join("category").get("id").in(propertySearchRequest.getCategoryIds()));
			}

			if (CollectionUtils.isNotEmpty(propertySearchRequest.getProjectIds())) {
				predicates.add(root.get("id").in(propertySearchRequest.getProjectIds()));
			}

			if (StringUtils.isNotBlank(propertySearchRequest.getCountryId())) {
				predicates.add(criteriaBuilder.equal(root.get(Project.Fields.countryId),
						propertySearchRequest.getCountryId()));
			}

			if (StringUtils.isNotBlank(propertySearchRequest.getStateId())) {
				predicates.add(
						criteriaBuilder.equal(root.get(Project.Fields.stateId), propertySearchRequest.getStateId()));
			}

			if (StringUtils.isNotBlank(propertySearchRequest.getCityId())) {
				predicates
						.add(criteriaBuilder.equal(root.get(Project.Fields.cityId), propertySearchRequest.getCityId()));
			}

			if (StringUtils.isNotBlank(propertySearchRequest.getDistrictId())) {
				predicates.add(criteriaBuilder.equal(root.get(Project.Fields.districtId),
						propertySearchRequest.getDistrictId()));
			}

			if (propertySearchRequest.getLongitude() != null && propertySearchRequest.getLatitude() != null) {
				predicates.add(criteriaBuilder.equal(root.get(Project.Fields.longitude),
						propertySearchRequest.getLongitude()));
				predicates.add(
						criteriaBuilder.equal(root.get(Project.Fields.latitude), propertySearchRequest.getLatitude()));
			}

			if (query.getResultType() != Long.class) {
				root.fetch(Project.Fields.developer, JoinType.INNER);
				root.fetch(Project.Fields.images, JoinType.INNER);
			}

			return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
		};
	}

}
