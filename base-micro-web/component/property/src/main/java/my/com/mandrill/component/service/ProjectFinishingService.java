package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.ProjectUnit;
import my.com.mandrill.component.domain.PropertyFinishingParameter;
import my.com.mandrill.utilities.general.dto.request.ObjectRequest;

import java.util.List;
import java.util.Set;

public interface ProjectFinishingService {

	void upsert(String unitId, Set<ObjectRequest> data);

	List<PropertyFinishingParameter> findByUnit(ProjectUnit unitId);

}
