package my.com.mandrill.component.service.impl;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Project;
import my.com.mandrill.component.domain.ProjectPropertyType;
import my.com.mandrill.component.domain.PropertySubType;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.ProjectPropertyTypeRepository;
import my.com.mandrill.component.service.ProjectPropertyTypeService;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.dto.request.ObjectRequest;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class ProjectPropertyTypeServiceImpl implements ProjectPropertyTypeService {

	private final ProjectPropertyTypeRepository projectPropertyTypeRepository;

	@Override
	@Transactional
	public void upsert(@NotBlank String projectId, @NotNull Set<ObjectRequest> propertyTypes) {
		/*
		 * Here we look for existing data based on project ID and type ID, because we
		 * don't want duplicate data. 1 project 1 subtype must be uniq
		 */
		Map<String, ProjectPropertyType> existing = projectPropertyTypeRepository
				.findByProjectIdAndTypeIdIn(projectId, propertyTypes.stream().map(ObjectRequest::getId).toList())
				.stream().collect(Collectors.toMap(v -> v.getType().getId(), Function.identity()));

		Project project = new Project();
		project.setId(projectId);
		List<ProjectPropertyType> projectPropertyTypes = propertyTypes.stream().map(id -> {
			ProjectPropertyType projectPropertyType = new ProjectPropertyType();
			projectPropertyType.setProject(project);

			PropertySubType propertySubType = new PropertySubType();
			propertySubType.setId(id.getId());
			projectPropertyType.setType(propertySubType);

			if (existing.containsKey(id.getId())) {
				projectPropertyType.setId(existing.get(id.getId()).getId());
			}
			return projectPropertyType;
		}).toList();
		List<ProjectPropertyType> inserted = projectPropertyTypeRepository.saveAll(projectPropertyTypes);

		/*
		 * For example, 2 data were previously sent, so 2 data are stored, but there
		 * should be 3 data, we will delete the 1 other data.
		 */
		List<String> insertedIds = inserted.stream().map(AuditSection::getId).toList();
		int effected = projectPropertyTypeRepository.deleteByProjectIdAndIdNotIn(projectId, insertedIds);
		log.debug("deleted project property type {} effected", effected);
	}

	@Override
	public List<ProjectPropertyType> findByProject(List<String> projectIds) {
		return projectPropertyTypeRepository.findByProjectIdIn(projectIds);
	}

	@Override
	public ProjectPropertyType findByProjectIdAndTypeId(String projectId, String typeId) {
		return projectPropertyTypeRepository.findByProjectIdAndTypeId(projectId, typeId)
				.orElseThrow(() -> new BusinessException(ErrorCodeEnum.PROJECT_PROPERTY_TYPE_NOT_FOUND));
	}

	@Override
	public ProjectPropertyType findById(String id) {
		return projectPropertyTypeRepository.findById(id)
				.orElseThrow(() -> new BusinessException(ErrorCodeEnum.PROJECT_PROPERTY_TYPE_NOT_FOUND));
	}

}
