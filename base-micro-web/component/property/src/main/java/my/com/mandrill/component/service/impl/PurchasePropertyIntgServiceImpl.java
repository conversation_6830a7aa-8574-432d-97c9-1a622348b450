package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.RunningNumberModule;
import my.com.mandrill.component.domain.Project;
import my.com.mandrill.component.domain.ProjectLike;
import my.com.mandrill.component.domain.ProjectUnit;
import my.com.mandrill.component.domain.PropertyInterest;
import my.com.mandrill.component.dto.model.ProjectBlockDTO;
import my.com.mandrill.component.dto.model.ProjectDuplexDTO;
import my.com.mandrill.component.dto.model.ProjectLotDTO;
import my.com.mandrill.component.dto.model.ProjectShoplotDTO;
import my.com.mandrill.component.dto.request.PropertyInterestRequest;
import my.com.mandrill.component.dto.request.PropertySearchRequest;
import my.com.mandrill.component.dto.response.PropertyParameterResponse;
import my.com.mandrill.component.dto.response.PropertyUnitDetailResponse;
import my.com.mandrill.component.dto.response.PurchasePropertyDetailResponse;
import my.com.mandrill.component.dto.response.PurchasePropertySearchResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.ProjectLikeRepository;
import my.com.mandrill.component.repository.jpa.spec.ProjectLikeSpecification;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.client.MoneyXCoreFeignClient;
import my.com.mandrill.utilities.feign.dto.CityDto;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.DistrictDto;
import my.com.mandrill.utilities.feign.dto.PostcodeDTO;
import my.com.mandrill.utilities.feign.dto.request.FindRelationTypeRequest;
import my.com.mandrill.utilities.general.constant.PropertyBuildingType;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PurchasePropertyIntgServiceImpl implements PurchasePropertyIntgService {

	private final ProjectService projectService;

	private final ProjectUnitService projectUnitService;

	private final ProjectLikeRepository projectLikeRepository;

	private final CommonFeignClient commonFeignClient;

	private final PropertyInterestService propertyInterestService;

	private final RunningNumberUtil runningNumberUtil;

	private final AccountFeignClient accountFeignClient;

	private final PropertyLikeService propertyLikeService;

	private final PropertyParameterService propertyParameterService;

	private final ProjectDuplexService projectDuplexService;

	private final ProjectLotService projectLotService;

	private final ProjectShoplotService projectShoplotService;

	private final ProjectBlockService projectBlockService;

	private final MoneyXCoreFeignClient moneyXCoreFeignClient;

	@Override
	public Page<PurchasePropertySearchResponse> searchProperty(PropertySearchRequest propertySearchRequest,
			String userId, Pageable pageable) {
		Page<Project> projects = projectService.findAll(propertySearchRequest, pageable);

		List<String> projectIds = projects.getContent().stream().map(Project::getId).toList();

		List<ProjectUnit> projectUnits = projectUnitService.findByProjectIdIn(projectIds, true);

		Map<String, ProjectUnit> cheapestProjectUnitMap = projectUnits.stream()
				.collect(Collectors.groupingBy(projectUnit -> projectUnit.getProject().getId(),
						Collectors.reducing(
								(unit1, unit2) -> unit1.getPrice().compareTo(unit2.getPrice()) < 0 ? unit1 : unit2)))
				.entrySet().stream()
				.collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().orElseGet(() -> projectUnits
						.stream().filter(unit -> unit.getProject().getId().equals(entry.getKey())).findFirst().get())));

		Map<String, ProjectLike> projectLikeMap = projectLikeRepository.findByProjectIdInAndUserId(projectIds, userId)
				.stream().collect(Collectors.toMap(ProjectLike::getProjectId, projectLike -> projectLike));

		List<PostcodeDTO> postcodes = commonFeignClient.getAllPostcode();

		List<CityDto> cities = commonFeignClient.getAllCities();

		List<PurchasePropertySearchResponse> purchasePropertySearchResponses = projects.stream()
				.map(project -> MapStructConverter.MAPPER.toPurchasePropertySearchResponse(project,
						projectLikeMap.get(project.getId()), cheapestProjectUnitMap.get(project.getId()), postcodes,
						cities))
				.toList();
		return new PageImpl<>(purchasePropertySearchResponses, projects.getPageable(), projects.getTotalElements());
	}

	@Override
	public List<PurchasePropertySearchResponse> myFavorites(Instant cursor, int limit) {
		String userId = SecurityUtil.currentUserId();
		Specification<ProjectLike> spec = ProjectLikeSpecification.filterWithUserIdAndCreatedDate(userId, cursor);
		Pageable pageable = PageRequest.of(0, limit, Sort.by("createdDate").descending());
		List<String> projectIds = projectLikeRepository.findAll(spec, pageable).stream().map(ProjectLike::getProjectId)
				.toList();
		if (CollectionUtils.isEmpty(projectIds)) {
			return new ArrayList<>();
		}
		return searchProperty(PropertySearchRequest.builder().projectIds(projectIds).build(), userId, pageable).stream()
				.toList();
	}

	@Override
	public PurchasePropertyDetailResponse getProjectDetail(String id) {
		Project project = projectService.findByIdAndActive(id);
		DistrictDto districtDto = commonFeignClient.getDistrict(project.getDistrictId());
		CityDto cityDto = commonFeignClient.getCity(project.getCityId());
		PostcodeDTO postcodeDTO = commonFeignClient.getPostcode(project.getPostcodeId());
		PropertyParameterResponse tenure = propertyParameterService.findById(project.getTenure().getId());
		PropertyParameterResponse category = propertyParameterService.findById(project.getCategory().getId());

		Map<PropertyBuildingType, Object> buildingTypeObjectMap = buildingTypeMap(project);
		List<PropertyUnitDetailResponse> propertyDetails = populatePropertyDetail(project, buildingTypeObjectMap);

		PurchasePropertyDetailResponse response = MapStructConverter.MAPPER.toPurchasePropertyDetailResponse(project);
		response.setCountryName(districtDto.getState().getCountry().getName());
		response.setStateName(districtDto.getState().getName());
		response.setCityName(cityDto.getName());
		response.setDistrictName(districtDto.getName());
		response.setPostcode(postcodeDTO.getPostcode());
		response.setTenure(tenure.getName());
		response.setCategory(category.getName());
		response.setLike(
				projectLikeRepository.existsByUserIdAndProjectId(SecurityUtil.currentUserId(), project.getId()));
		response.setPropertyTypes(project.getPropertyTypes().stream()
				.map(projectPropertyType -> projectPropertyType.getType().getName()).collect(Collectors.toSet()));
		response.setFacilities(project.getFacilities().stream()
				.map(propertyFacilitiesParameter -> propertyFacilitiesParameter.getPropertyParameter().getName())
				.collect(Collectors.toSet()));
		response.setPropertyDetails(propertyDetails);
		response.setBlock((ProjectBlockDTO) buildingTypeObjectMap.get(PropertyBuildingType.BLOCK));
		response.setShoplot((ProjectShoplotDTO) buildingTypeObjectMap.get(PropertyBuildingType.SHOPLOT));
		response.setLot((ProjectLotDTO) buildingTypeObjectMap.get(PropertyBuildingType.LOT));
		response.setDuplex((ProjectDuplexDTO) buildingTypeObjectMap.get(PropertyBuildingType.DUPLEX));
		response.setImages(project.getImages().stream().map(MapStructConverter.MAPPER::toProjectImageDTO).toList());
		return response;
	}

	private List<PropertyUnitDetailResponse> populatePropertyDetail(Project project,
			Map<PropertyBuildingType, Object> buildingTypeObjectMap) {
		return project.getProjectUnits().stream().filter(ProjectUnit::getActive).map(projectUnit -> {
			Object obj = buildingTypeObjectMap.get(projectUnit.getBuildingType());
			PropertyUnitDetailResponse response = MapStructConverter.MAPPER.toProjectUnitDetailResponse(projectUnit);
			response.setFinishings(projectUnit.getFinishing().stream()
					.map(propertyFinishingParameter -> propertyFinishingParameter.getPropertyParameter().getName())
					.toList());
			response.setImages(
					projectUnit.getImages().stream().map(MapStructConverter.MAPPER::toProjectImageDTO).toList());
			response.setPropertyTypeName(projectUnit.getUnitType().getType().getName());
			response.setBlock(obj instanceof ProjectBlockDTO ? (ProjectBlockDTO) obj : null);
			response.setShoplot(obj instanceof ProjectShoplotDTO ? (ProjectShoplotDTO) obj : null);
			response.setLot(obj instanceof ProjectLotDTO ? (ProjectLotDTO) obj : null);
			response.setDuplex(obj instanceof ProjectDuplexDTO ? (ProjectDuplexDTO) obj : null);
			return response;
		}).toList();
	}

	private Map<PropertyBuildingType, Object> buildingTypeMap(Project project) {
		Map<PropertyBuildingType, Object> map = new HashMap<>();
		project.getBuildingTypes().forEach(buildingType -> {
			switch (buildingType) {
				case SHOPLOT:
					projectShoplotService.findByProjectId(project.getId()).map(MapStructConverter.MAPPER::toShoplotDto)
							.ifPresent(dto -> map.put(buildingType, dto));
					break;
				case LOT:
					projectLotService.findByProjectId(project.getId()).map(MapStructConverter.MAPPER::toLotDto)
							.ifPresent(dto -> map.put(buildingType, dto));
					break;
				case DUPLEX:
					projectDuplexService.findByProjectId(project.getId()).map(MapStructConverter.MAPPER::toDuplexDto)
							.ifPresent(dto -> map.put(buildingType, dto));
					break;
				case BLOCK:
					projectBlockService.findByProjectId(project.getId()).map(MapStructConverter.MAPPER::toBlockDto)
							.ifPresent(dto -> map.put(buildingType, dto));
					break;
				default:
					throw new IllegalArgumentException("Unknown PropertyBuildingType: " + buildingType);
			}
		});
		return map;
	}

	@Override
	public void interest(PropertyInterestRequest request) {
		PropertyInterest propertyInterest = createPropertyInterest(request);
		if (StringUtils.isNotBlank(request.getUnitId())) {
			ProjectUnit projectUnit = projectUnitService.findById(request.getUnitId());
			propertyInterest.setProject(projectUnit.getProject());
			propertyInterest.setProjectUnit(projectUnit);
		}
		else {
			Project project = projectService.findById(request.getProjectId());
			propertyInterest.setProject(project);
		}

		RSMRelationType rsmRelation = moneyXCoreFeignClient
				.findRelationType(FindRelationTypeRequest.builder().referredId(propertyInterest.getUserId()).build());

		propertyInterest.setRsmEligible(true);
		propertyInterest.setRsmStatus(RSMStatus.PENDING);
		propertyInterest.setRsmRelation(rsmRelation);
		handleSource(propertyInterest, request);

		propertyInterestService.save(propertyInterest);
	}

	@Override
	public void toggleLike(String projectId) {
		propertyLikeService.toggleLike(SecurityUtil.currentUserId(), projectId);
	}

	private PropertyInterest createPropertyInterest(PropertyInterestRequest request) {
		CurrentUserIdDTO currentUser = accountFeignClient.getCurrentUserId();
		return PropertyInterest.builder()
				.applicationId(runningNumberUtil
						.getLatestRunningNumber(RunningNumberModule.PURCHASE_PROPERTY_MODULE.name(), false))
				.email(request.getEmail()).appointmentDate(request.getAppointmentDate())
				.mobileNumber(request.getMobileNumber()).nric(currentUser.getNric()).income(request.getIncome())
				.name(request.getName()).userRefNo(currentUser.getRefNo()).userId(currentUser.getId()).build();
	}

	private void handleSource(PropertyInterest propertyInterest, PropertyInterestRequest propertyInterestRequest) {
		if (propertyInterestRequest == null || propertyInterestRequest.getSource() == null || propertyInterest == null)
			return;

		final UserInterestedSource source = propertyInterestRequest.getSource();

		propertyInterest.setSource(propertyInterestRequest.getSource());
		if (source == UserInterestedSource.ADVERTISEMENT) {
			if (StringUtils.isBlank(propertyInterestRequest.getSourceId())) {
				throw new BusinessException(ErrorCodeEnum.MISSING_SOURCE_ID_FOR_ADVERTISEMENT_SOURCE);
			}

			propertyInterest.setSourceId(propertyInterestRequest.getSourceId());
		}

	}

}
