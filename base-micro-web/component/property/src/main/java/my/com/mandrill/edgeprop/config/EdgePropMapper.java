package my.com.mandrill.edgeprop.config;

import my.com.mandrill.edgeprop.domain.EdgePropAsset;
import my.com.mandrill.edgeprop.domain.EdgePropERP;
import my.com.mandrill.edgeprop.dto.model.EdgePropAssetDTO;
import my.com.mandrill.edgeprop.dto.model.EdgePropERPDTO;
import my.com.mandrill.edgeprop.dto.request.ERPRequest;
import my.com.mandrill.edgeprop.dto.request.EdgePropERPRequest;
import my.com.mandrill.edgeprop.dto.response.AssetResponse;
import my.com.mandrill.edgeprop.dto.response.ERPResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EdgePropMapper {

	EdgePropMapper MAPPER = Mappers.getMapper(EdgePropMapper.class);

	EdgePropERPRequest toEdgePropERPRequest(ERPRequest erpRequest);

	@Mapping(target = "salePropertyName", source = "sale.propertyName")
	@Mapping(target = "saleAddress", source = "sale.address")
	@Mapping(target = "saleLandSize", source = "sale.landSize")
	@Mapping(target = "saleFloorArea", source = "sale.floorArea")
	@Mapping(target = "saleERP", source = "sale.edgeRefPrice")
	@Mapping(target = "saleERPPsf", source = "sale.edgeRefPricePsf")
	@Mapping(target = "saleERPMin", source = "sale.edgeRefPriceMin")
	@Mapping(target = "saleERPMax", source = "sale.edgeRefPriceMax")
	@Mapping(target = "saleERPPsfMin", source = "sale.edgeRefPricePsfMin")
	@Mapping(target = "saleERPPsfMax", source = "sale.edgeRefPricePsfMax")
	EdgePropERP toEdgePropERP(ERPResponse erpResponse);

	EdgePropERPDTO toEdgePropERPModel(EdgePropERP edgePropERP);

	EdgePropAsset toEdgePropAsset(AssetResponse assetResponse);

	EdgePropAssetDTO toEdgePropAssetDTO(EdgePropAsset edgePropAsset);

}