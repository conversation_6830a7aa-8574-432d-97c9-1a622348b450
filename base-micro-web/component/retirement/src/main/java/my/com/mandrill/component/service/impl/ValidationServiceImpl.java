package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.RetirementAccountService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
public class ValidationServiceImpl implements ValidationService {

	private final RetirementAccountService retirementAccountService;

	@Override
	public void validateRetirementAge(Integer retirementAge, Integer lifeExpectancy) {
		if (retirementAge >= lifeExpectancy) {
			throw new BusinessException(ErrorCodeEnum.RETIREMENT_AGE_OVER_LIFE_EXPECTANCY);
		}
	}

	@Override
	public void validateRetirementSavingsAmount(BigDecimal savingsAmount) {
		if (savingsAmount.compareTo(BigDecimal.ZERO) <= 0) {
			throw new BusinessException(ErrorCodeEnum.INVALID_SAVINGS_AMOUNT);
		}
	}

	@Override
	public void validateRetirementAccountExists(String userId, String accountTypeId, String providerId) {
		if (retirementAccountService.existsByUserIdAndAccountTypeIdAndRetirementProviderId(userId, accountTypeId,
				providerId)) {
			throw new BusinessException(ErrorCodeEnum.RETIREMENT_ACCOUNT_EXISTS);
		}
	}

	@Override
	public void validateDeleteRetirementAccount(String accountId, String userId) {
		if (!retirementAccountService.existsByIdAndUserId(accountId, userId)) {
			throw new BusinessException(ErrorCodeEnum.RETIREMENT_ACCOUNT_NOT_FOUND);
		}
	}

}
