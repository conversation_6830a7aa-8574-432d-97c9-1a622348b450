package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.Year;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModelYearDTO implements Serializable {

	private String id;

	@Schema(implementation = Short.class, example = "1997")
	@JsonFormat(shape = JsonFormat.Shape.NUMBER_INT, pattern = "yyyy")
	private Year value;

	private ModelDTO model;

}
