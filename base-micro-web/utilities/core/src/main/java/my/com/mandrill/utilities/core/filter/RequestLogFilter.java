package my.com.mandrill.utilities.core.filter;

import jakarta.servlet.AsyncEvent;
import jakarta.servlet.AsyncListener;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.util.TraceContextHelper;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
@Component
@RequiredArgsConstructor
public class RequestLogFilter extends OncePerRequestFilter {

	private static final Set<String> SENSITIVE_HEADERS = Set.of("authorization", "cookie", "set-cookie",
			"x-internal-api-key");

	private final TraceContextHelper traceContextHelper;

	@Override
	protected void doFilterInternal(@NonNull HttpServletRequest req, @NonNull HttpServletResponse res,
			FilterChain chain) throws ServletException, IOException {

		// Check if this is a streaming request
		String acceptHeader = req.getHeader("Accept");
		boolean isStreaming = acceptHeader != null && acceptHeader.contains("text/event-stream");

		log.info("isStreaming: {}", isStreaming);
		if (req.getRequestURI().contains("/v2/chats")) {
			// For streaming requests, don't use caching wrappers
			long start = System.currentTimeMillis();
			chain.doFilter(req, res);
			long duration = System.currentTimeMillis() - start;
			log.info("[HTTP STREAMING] {} {} | {} ms", req.getMethod(), req.getRequestURI(), duration);
			return;
		}

		ContentCachingRequestWrapper request = new ContentCachingRequestWrapper(req);
		ContentCachingResponseWrapper response = new ContentCachingResponseWrapper(res);
		long start = System.currentTimeMillis();

		try {
			chain.doFilter(request, response);
		}
		finally {
			if (!request.isAsyncStarted()) {
				logAndCopy(request, response, start);
			}
			else {
				request.getAsyncContext().addListener(new AsyncListener() {
					@Override
					public void onComplete(AsyncEvent event) throws IOException {
						logAndCopy(request, response, start);
					}

					@Override
					public void onTimeout(AsyncEvent e) {
					}

					@Override
					public void onStartAsync(AsyncEvent e) {
					}

					@Override
					public void onError(AsyncEvent e) {
					}
				});
			}
		}
	}

	private void logAndCopy(ContentCachingRequestWrapper req, ContentCachingResponseWrapper res, long start)
			throws IOException {

		long duration = System.currentTimeMillis() - start;

		if (log.isInfoEnabled() && !shouldSkipLogging(req.getRequestURI())) {
			if (ContentType.APPLICATION_JSON.getMimeType().equals(res.getContentType())) {
				String requestBody = new String(req.getContentAsByteArray(), UTF_8);
				String responseBody = new String(res.getContentAsByteArray(), UTF_8);
				log.info("""
						[HTTP] %s %s | %d ms
						- RequestId: %s
						- Platform : %s
						- Headers   : %s
						- Req Body  : %s
						- Res Body  : %s
						""".formatted(req.getMethod(), req.getRequestURI(), duration, traceContextHelper.getRequestId(),
						traceContextHelper.getPlatformClient(), headerMap(req), requestBody, responseBody));
			}
			else {
				log.info("""
						[HTTP] %s %s | %d ms
						- RequestId: %s
						- Platform : %s
						- Headers   : %s
						""".formatted(req.getMethod(), req.getRequestURI(), duration, traceContextHelper.getRequestId(),
						traceContextHelper.getPlatformClient(), headerMap(req)));
			}
		}
		res.copyBodyToResponse();
	}

	private String headerMap(HttpServletRequest req) {
		return Collections.list(req.getHeaderNames()).stream().collect(Collectors.toMap(h -> h,
				h -> SENSITIVE_HEADERS.contains(h.toLowerCase()) ? "******" : req.getHeader(h))).toString();
	}

	private boolean shouldSkipLogging(String uri) {
		log.info("chatbot uri : {}", uri.contains("/v2/chats"));

		return uri.startsWith("/v3/api-docs") || uri.startsWith("/swagger-ui") || uri.startsWith("/swagger-resources")
				|| uri.contains("/v2/chats");
	}

}
