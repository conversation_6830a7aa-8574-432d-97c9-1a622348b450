package my.com.mandrill.utilities.core.retry;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.retry.support.RetryTemplate;

import java.util.Optional;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

public class RetryUtilTest {

	private RetryUtil retryUtil;

	@BeforeEach
	void setUp() {
		RetryTemplate retryTemplate = RetryTemplate.builder().fixedBackoff(2000).maxAttempts(3).build();
		retryUtil = new RetryUtil(retryTemplate);
	}

	@Test
	void testSafeRetrySuccess() {
		Supplier<Optional<String>> resultSupplier = mock(Supplier.class);
		when(resultSupplier.get()).thenReturn(Optional.of("Success"));

		Optional<String> result = retryUtil.safeRetry(resultSupplier);

		assertTrue(result.isPresent());
		assertEquals("Success", result.get());

		verify(resultSupplier, times(1)).get();
	}

	@Test
	void testRetryFailureWithSafeRetry() {
		Supplier<Optional<String>> resultSupplier = mock(Supplier.class);
		when(resultSupplier.get()).thenReturn(Optional.empty()).thenReturn(Optional.empty())
				.thenReturn(Optional.of("Success"));

		Optional<String> result = retryUtil.safeRetry(resultSupplier);

		assertTrue(result.isPresent());
		assertEquals("Success", result.get());
		verify(resultSupplier, times(3)).get();
	}

	@Test
	void testSafeRetryFailureExceedingMaxAttempts() {
		Supplier<Optional<String>> resultSupplier = mock(Supplier.class);
		when(resultSupplier.get()).thenReturn(Optional.empty());
		Optional<String> result = retryUtil.safeRetry(resultSupplier);

		assertTrue(result.isEmpty());
		verify(resultSupplier, times(3)).get();
	}

}
