package my.com.mandrill.utilities.feign.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInterestedRedirectDTO implements Serializable {

	private String createdDate;

	private String issuerType;

	private String provider;

	private String productType;

	private String productName;

	private String fullName;

	private String phoneNumber;

	private String email;

	private String userRefNo;

	private Long num;

}
