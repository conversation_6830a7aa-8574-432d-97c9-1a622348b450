package my.com.mandrill.utilities.feign.dto.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import my.com.mandrill.utilities.general.constant.Currency;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.RecurringType;
import my.com.mandrill.utilities.general.constant.Status;
import my.com.mandrill.utilities.general.serializer.MoneySerializer;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentTransactionDTO implements Serializable {

	private String id;

	private String fullName;

	private String phoneNumber;

	private String email;

	private String version;

	private MerchantDTO merchant;

	private String refNo;

	private Currency currency;

	@JsonSerialize(using = MoneySerializer.class)
	private BigDecimal amount;

	private String requestSignature;

	private String productDescription;

	private String clientIp;

	private Status status;

	private EntityName entityName;

	private String entityId;

	private String paymentId;

	private String responseDescription;

	private String paymentStatus;

	private String paymentType;

	private Instant dateCompleted;

	private RecurringType recurringType;

	private TransactionRefundDTO refund;

}