package my.com.mandrill.utilities.general.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KafkaTopic {

	SINGLE_PARTITION(1, 1), LOW_PARTITIONS(3, 1), MEDIUM_PARTITIONS(6, 1), HIGH_PARTITIONS(10, 1);

	public static final String REMINDER_CLEAN_UP_TOPIC = "reminder-clean-up";

	public static final String SEND_SMS_TOPIC = "send-sms";

	public static final String SEND_WHATSAPP_TOPIC = "send-whatsapp";

	public static final String DASHBOARD_ACTIVITY = "dashboard-activity";

	public static final String EXCEPTION_STACKTRACE = "exception-stacktrace";

	public static final String SEND_EMAIL_TOPIC = "send-email";

	public static final String EKYC_CALLBACK_TOPIC = "ekyc-callback";

	public static final String UPDATE_USER_TOPIC = "update-user";

	public static final String FINOLOGY_UPDATE_USER_TOPIC = "finology-update-user";

	public static final String FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC = "finology-update-use-identity-number";

	public static final String CREATE_OR_UPDATE_INCOME = "create-or-update-income";

	public static final String FINOLOGY_PAYMENT_CALLBACK_TOPIC = "finology-payment-callback";

	public static final String FINOLOGY_UPDATE_VEHICLE_ROAD_TAX_TOPIC = "finology-update-vehicle-road-tax-callback";

	public static final String FINOLOGY_UPDATE_VEHICLE_INSURANCE_RENEWAL_DATE_TOPIC = "finology-update-vehicle-insurance-renewal";

	public static final String SEND_NOTIFICATION = "send-notification";

	public static final String E_VISIT_REMINDER_CALLBACK = "e_visit_reminder_callback";

	public static final String E_VISIT_SCHEDULER_CALLBACK = "e_visit_scheduler_callback";

	public static final String PROPERTY_DELETION = "property-deletion";

	public static final String VEHICLE_DELETION = "vehicle-deletion";

	public static final String SCHEDULER_PUSH_NOTIFICATION = "scheduler_push_notification";

	public static final String PRE_PAID_TOP_UP_REMINDER = "pre-paid-top-up-reminder";

	public static final String PRE_PAID_TOP_UP_INBOX = "pre-paid-top-up-inbox";

	public static final String PROCESS_INCOMPLETE_SURVEY_REMINDER = "process-incomplete-survey-reminder";

	public static final String CHECK_RENEWABLE_VEHICLE_INSURANCE = "check-renewable-vehicle-insurance";

	public static final String CREATE_REMINDER_VEHICLE_INSURANCE_EXPIRE = "create-reminder-vehicle-insurance-expire";

	public static final String BANK_DETAIL_CREATED_EVENT = "bank-detail-created-event";

	public static final String BANK_DETAIL_UPDATED_EVENT = "bank-detail-updated-event";

	public static final String BANK_DETAIL_DELETED_EVENT = "bank-detail-deleted-event";

	public static final String INSURANCE_RENEWAL_DATE_UPDATED_EVENT = "insurance-renewal-date-updated";

	public static final String UPDATE_USER_SURVEY_SALARY_RANGE = "update-user-survey-salary-range";

	public static final String FINOLOGY_UPDATE_VEHICLE_RENEWAL_DATE_AND_MARKET_VALUE_TOPIC = "finology-update-vehicle-renewal-date-and-market-value";

	public static final String USER_ACTIVITY_TRANSACTION = "user-activity-transaction";

	public static final String PAYMENT_INFO_UPDATE_TOPIC = "payment-info-update-topic";

	public static final String AUTO_COMPLETE_UNCOMPLETED_TASK = "auto-complete-uncompleted-task";

	public static final String SAVING_GOALS_DELETION = "saving-goals-deletion";

	public static final String USER_REFEREE_UPDATE_TOPIC = "user-referee-update-topic";

	public static final String RSM_CLOSING_BALANCE_REPORT_SCHEDULER_TOPIC = "closing-balance-report-scheduler-topic";

	public static final String RSM_ACCOUNT_DELETION_UPDATE_TOPIC = "rsm-account-deletion-update-topic";

	public static final String SURVEY_EVENT_TOPIC = "survey-event-topic";

	public static final String RESOLVE_FAILED_PREPAID_TOP_UP = "resolve-failed-prepaid-top-up";

	public static final String ARCHIVE_NOTIFICATION_INBOX = "archive-notification-inbox";

	public static final String FINOLOGY_SYNC_RENEWAL_DATE_TOPIC = "finology-sync-renewal-date-topic";

	public static final String EPF_SAVING_CALCULATION = "epf-saving-calculation";

	public static final String RSM_POINT_EXPIRY_CLEAN_UP_SCHEDULER_TOPIC = "rsm-point-expiry-clean-up-scheduler-topic";

	public static final String SET_RSM_HEADER_LIVE_ENDED_REMINDER_TOPIC = "set-rsm-header-live-ended-reminder-topic";

	public static final String RSM_POINT_EXPIRY_REMINDER_SCHEDULER_TOPIC = "rsm-point-expiry-reminder-scheduler-topic";

	private final int partitions;

	private final int replicas;

}
