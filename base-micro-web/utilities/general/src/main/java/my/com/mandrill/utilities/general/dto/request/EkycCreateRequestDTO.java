package my.com.mandrill.utilities.general.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class EkycCreateRequestDTO implements Serializable {

	private String fullName;

	private String applicantId;

	private String idNo;

	private String idDocType;

	private String clientReferenceId;

	private String userId;

}
